#!/bin/bash

# PM2 应用管理脚本
# 用于管理产品展示系统的前后端服务

PM2_CONFIG="/root/products-b-test/products-b-test/ecosystem.config.js"
LOG_DIR="/root/products-b-test/products-b-test/logs"

show_usage() {
    echo "用法: $0 {start|stop|restart|status|logs|monit}"
    echo ""
    echo "命令说明:"
    echo "  start   - 启动所有服务"
    echo "  stop    - 停止所有服务"
    echo "  restart - 重启所有服务"
    echo "  status  - 查看服务状态"
    echo "  logs    - 查看实时日志"
    echo "  monit   - 打开PM2监控界面"
    echo ""
    echo "服务信息:"
    echo "  后端服务: http://192.229.85.5:3000"
    echo "  前端服务: http://192.229.85.5:5174"
    echo "  日志目录: $LOG_DIR"
}

start_services() {
    echo "启动服务..."
    cd /root/products-b-test/products-b-test
    pm2 start $PM2_CONFIG
    echo "服务启动完成!"
    pm2 status
}

stop_services() {
    echo "停止服务..."
    pm2 stop all
    echo "服务已停止!"
}

restart_services() {
    echo "重启服务..."
    pm2 restart all
    echo "服务重启完成!"
    pm2 status
}

show_status() {
    echo "服务状态:"
    pm2 status
    echo ""
    echo "访问地址:"
    echo "  后端API: http://192.229.85.5:3000/api/v1/health"
    echo "  前端页面: http://192.229.85.5:5174"
}

show_logs() {
    echo "显示实时日志 (按 Ctrl+C 退出):"
    pm2 logs
}

show_monit() {
    echo "打开PM2监控界面:"
    pm2 monit
}

case "$1" in
    start)
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    monit)
        show_monit
        ;;
    *)
        show_usage
        exit 1
        ;;
esac

exit 0