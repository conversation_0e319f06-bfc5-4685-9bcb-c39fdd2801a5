/* 性能优化和硬件加速样式 */

/* 强制GPU加速的通用类 */
.transform-gpu {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
}

/* 滑动和滚动性能优化 */
.smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* 动画性能优化 */
.animate-gpu {
  transform: translate3d(0, 0, 0);
  will-change: transform, opacity;
  backface-visibility: hidden;
}

/* 图片优化 */
.image-optimized {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: optimize-contrast;
  transform: translateZ(0);
}

/* 悬停效果优化 */
.hover-lift {
  transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
}

.hover-lift:hover {
  transform: translateY(-4px) translateZ(0);
}

/* 响应式布局优化 */
.responsive-container {
  transition: margin-right 0.2s ease-out, padding 0.2s ease-out;
  will-change: margin-right, padding;
}

/* 滚动动画优化 */
.scroll-fade-in {
  opacity: 0;
  transform: translateY(20px) translateZ(0);
  transition: opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: opacity, transform;
}

.scroll-fade-in.in-view {
  opacity: 1;
  transform: translateY(0) translateZ(0);
}

/* 卡片动画优化 */
.card-hover {
  transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              box-shadow 0.2s ease-out;
  will-change: transform, box-shadow;
  backface-visibility: hidden;
}

.card-hover:hover {
  transform: translateY(-4px) scale(1.02) translateZ(0);
}

/* 模态框和面板优化 */
.modal-slide {
  transform: translateX(100%) translateZ(0);
  transition: transform 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
  backface-visibility: hidden;
}

.modal-slide.open {
  transform: translateX(0) translateZ(0);
}

/* 交错动画优化 */
.stagger-item {
  opacity: 0;
  transform: translateY(15px) translateZ(0);
  will-change: opacity, transform;
  backface-visibility: hidden;
}

.stagger-container.animate .stagger-item {
  animation: stagger-fade-in 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes stagger-fade-in {
  to {
    opacity: 1;
    transform: translateY(0) translateZ(0);
  }
}

/* 为不同的交错项添加延迟 */
.stagger-item:nth-child(1) { animation-delay: 0.05s; }
.stagger-item:nth-child(2) { animation-delay: 0.1s; }
.stagger-item:nth-child(3) { animation-delay: 0.15s; }
.stagger-item:nth-child(4) { animation-delay: 0.2s; }
.stagger-item:nth-child(5) { animation-delay: 0.25s; }
.stagger-item:nth-child(6) { animation-delay: 0.3s; }
.stagger-item:nth-child(7) { animation-delay: 0.35s; }
.stagger-item:nth-child(8) { animation-delay: 0.4s; }

/* 按钮交互优化 */
.button-press {
  transition: transform 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
  backface-visibility: hidden;
}

.button-press:active {
  transform: scale(0.98) translateZ(0);
}

/* 图片缩放优化 */
.image-scale {
  transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
  backface-visibility: hidden;
}

.image-scale:hover {
  transform: scale(1.05) translateZ(0);
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  .transform-gpu,
  .animate-gpu,
  .hover-lift,
  .scroll-fade-in,
  .card-hover,
  .modal-slide,
  .stagger-item,
  .button-press,
  .image-scale {
    transition: none !important;
    animation: none !important;
    transform: none !important;
  }
  
  .scroll-fade-in {
    opacity: 1;
  }
  
  .modal-slide.open {
    transform: none;
  }
}

/* 低性能设备优化 */
@media (max-width: 768px) {
  .transform-gpu {
    transform: none;
  }
  
  .card-hover:hover {
    transform: none;
  }
  
  .image-scale:hover {
    transform: none;
  }
}

/* 打印样式优化 */
@media print {
  .transform-gpu,
  .animate-gpu,
  .hover-lift,
  .scroll-fade-in,
  .card-hover,
  .modal-slide,
  .stagger-item,
  .button-press,
  .image-scale {
    transform: none !important;
    transition: none !important;
    animation: none !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .card-hover {
    transition: box-shadow 0.2s ease-out;
  }
  
  .card-hover:hover {
    transform: none;
    box-shadow: 0 0 0 2px currentColor;
  }
}