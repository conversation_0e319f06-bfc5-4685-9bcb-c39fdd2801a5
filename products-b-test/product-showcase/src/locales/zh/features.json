{"sync": {"title": "数据同步", "status": "同步状态", "lastSync": "上次同步", "nextSync": "下次同步", "inProgress": "同步进行中", "completed": "同步完成", "failed": "同步失败", "never": "从未同步", "startSync": "开始同步", "stopSync": "停止同步", "autoSync": "自动同步", "manualSync": "手动同步", "syncInterval": "同步间隔", "syncHistory": "同步历史", "totalRecords": "总记录数", "newRecords": "新增记录", "updatedRecords": "更新记录", "deletedRecords": "删除记录", "errorRecords": "错误记录", "syncLog": "同步日志", "viewDetails": "查看详情", "retry": "重试"}, "performance": {"title": "性能监控", "metrics": "性能指标", "loadTime": "加载时间", "renderTime": "渲染时间", "memoryUsage": "内存使用", "networkRequests": "网络请求", "cacheHitRate": "缓存命中率", "fps": "帧率", "optimization": "优化建议", "suggestions": "性能建议", "report": "性能报告", "benchmark": "基准测试", "profile": "性能分析"}, "api": {"title": "API 演示", "endpoint": "API 端点", "method": "HTTP 方法", "request": "请求", "response": "响应", "headers": "请求头", "parameters": "参数", "body": "请求体", "status": "状态码", "execute": "执行请求", "clear": "清空响应", "copy": "复制响应", "format": "格式化 JSON", "documentation": "API 文档"}}