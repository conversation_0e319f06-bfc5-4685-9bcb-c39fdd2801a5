module.exports = {
  apps: [
    {
      name: 'products-backend',
      script: './node_modules/.bin/ts-node',
      args: 'src/app.ts',
      cwd: '/root/products-b-test/products-b-test/products-backend',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'development',
        PORT: 3000,
        TS_NODE_PROJECT: 'tsconfig.json'
      },
      log_file: '/root/products-b-test/products-b-test/logs/backend-combined.log',
      out_file: '/root/products-b-test/products-b-test/logs/backend-out.log',
      error_file: '/root/products-b-test/products-b-test/logs/backend-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      max_memory_restart: '1G',
      restart_delay: 5000,
      max_restarts: 10,
      min_uptime: '10s',
      watch: false,
      ignore_watch: ['node_modules', 'logs', 'dist']
    },
    {
      name: 'products-frontend',
      script: 'npm',
      args: 'run dev',
      cwd: '/root/products-b-test/products-b-test/product-showcase',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'development',
        HOST: '0.0.0.0',
        PORT: 5173
      },
      log_file: '/root/products-b-test/products-b-test/logs/frontend-combined.log',
      out_file: '/root/products-b-test/products-b-test/logs/frontend-out.log',
      error_file: '/root/products-b-test/products-b-test/logs/frontend-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      max_memory_restart: '500M',
      restart_delay: 3000,
      max_restarts: 10,
      min_uptime: '10s',
      watch: false
    }
  ]
};