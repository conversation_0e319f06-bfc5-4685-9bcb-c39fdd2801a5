{"timestamp": "2025-07-22T04:09:42.153Z", "totalProducts": 1209, "summary": {"flavor": {"description": "口味", "priority": "high", "total": 1209, "missing": 145, "percentage": 12, "present": 1064}, "manufacturer": {"description": "生产商", "priority": "high", "total": 1209, "missing": 138, "percentage": 11.4, "present": 1071}, "specification": {"description": "规格", "priority": "medium", "total": 1209, "missing": 61, "percentage": 5, "present": 1148}, "origin.city": {"description": "产地城市", "priority": "low", "total": 1209, "missing": 92, "percentage": 7.6, "present": 1117}, "barcode": {"description": "条码", "priority": "low", "total": 1209, "missing": 983, "percentage": 81.3, "present": 226}}, "missingData": {"flavor": [{"productId": "reccAAZZoJ", "name": "Cooked salted duck eggs (FP)", "platform": "大润发", "category": "肉蛋水产", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recSIMFoD4", "name": "Preserved eggs with pine flower flavor", "platform": "大润发", "category": "肉蛋水产", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recusdDm8K", "name": "Pressed first-grade peanut oil", "platform": "胖东来", "category": "粮油干调", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recWSuLZNV", "name": "Sunflower oil with zero trans fat", "platform": "胖东来", "category": "粮油干调", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recEY0ZC9N", "name": "Dried apples", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recQtX1bHJ", "name": "100% Coconut water", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recPcj2J2z", "name": "<PERSON>ofeng Free Love (Baijiu)", "platform": "胖东来", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recZKuhdMR", "name": "Craft black beer", "platform": "胖东来", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recfgjPAKS", "name": "Craft wheat beer", "platform": "胖东来", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdNEzVxJ", "name": "Coconut water", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rechixM3PZ", "name": "Craft wheat beer", "platform": "胖东来", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recnrLbrbJ", "name": "Coconut drink", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recEPv0sT6", "name": "Dried red dragon fruit", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec03L0cs9", "name": "Freshly extracted freeze-dried hazelnuts", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recjulYPLB", "name": "Jiaguoyuan 100% NFC Black Diamond Pineapple Juice", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recRPLG1Yf", "name": "Pine mushroom crispy sunflower kernels", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec44iYdJE", "name": "YangShengTang Birch Tree Sap", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rechgC5KR5", "name": "Heytea Kale and Cucumber Compound Fruit and Vegetable Juice Drink", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recqHY5CEk", "name": "Chilean seedless prunes", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recAgc4T9s", "name": "Daily nuts (nuts + dried fruits)", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recPw3sGe9", "name": "Organic fruit juice, dried blueberries", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recJ0sfa20", "name": "<PERSON><PERSON> Small Dried Mandarins", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recexGxJNE", "name": "Organic savory peanuts", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recS5oC1J8", "name": "Apple and Astragalus Drink", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recgF69wR0", "name": "Member's <PERSON>", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recEE6E3C2", "name": "Dole Turkish Dried Apricots", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recXcG3YSt", "name": "Organic freeze-dried strawberries", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recbze5e6N", "name": "Organic chestnut kernels", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec7LAQJ7s", "name": "Snow pear and loquat syrup", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuvHQXhK", "name": "Chocolate milk drink", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recLZaX6e0", "name": "Succulent Juicy Grapefruit Mixed Fruit Juice Drink", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec7auo3fM", "name": "100% NFC Orange Juice", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec5VqTHCj", "name": "Nut milk plant protein beverage", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recvJ47BgP", "name": "Freeze-dried lemon slices with honey", "platform": "大润发", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec5sYwfGA", "name": "Organic large oat flakes", "platform": "大润发", "category": "麦片", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recfzzOtxU", "name": "Greek-style strained yogurt", "platform": "大润发", "category": "乳品烘焙", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recOGf7NkT", "name": "Shandong Sweet Potato Noodles", "platform": "大润发", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recJGEfmhY", "name": "Daily Freshness Dairy Little Freshness 4.0 Low-Fat Fresh Milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recxVWvI5d", "name": "Rice from the cold regions of Northeast China", "platform": "大润发", "category": "粮油干调", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recke6g2i1", "name": "Low-sugar Coconut and Nut Crisps", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reczNNMLe5", "name": "Spiced Braised <PERSON>", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recwmNhDRx", "name": "<PERSON><PERSON><PERSON> Hawthorn Juice", "platform": "大润发", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reclZYBP6i", "name": "Low-sugar Amber Walnuts", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recw<PERSON><PERSON><PERSON><PERSON>", "name": "Spiced Braised Duck <PERSON>", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recry6f4aX", "name": "Pecan nuts", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recJ1b83pK", "name": "100% NFC Freshly Squeezed Orange Juice with Rock Sugar", "platform": "大润发", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recRtypTfH", "name": "100% NFC Freshly Squeezed Ugly Apple Juice", "platform": "大润发", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recducNSAt", "name": "Purple-skinned cashew nuts", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recKypbjrt", "name": "Double Chocolate Crunchy Cereal", "platform": "大润发", "category": "麦片", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recZvNMMfG", "name": "Original Fragrance Black Wheat Soda Crackers", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recA6EUr8Q", "name": "Snack Cat Cat Mixed Vegetable and Fruit Crisps", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recyeD6DiF", "name": "<PERSON>'s Roasted Sunflower Seeds", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recCkZHTq4", "name": "Spiced and Braised Duck Gizzards", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recL6mDtoP", "name": "Slow-roasted Honey-glazed Chicken Drumettes", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reclyiTSmp", "name": "<PERSON>ma pomelo peel", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recMAhckFT", "name": "Hema Organic Chestnut Kernels", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recEvpzWbR", "name": "<PERSON><PERSON> Dried Tangerine Peel and Plum Strips", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recmfJXCc6", "name": "<PERSON><PERSON> C<PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recYb6EBoq", "name": "<PERSON><PERSON> and Ready-to-Eat Fresh-Locked <PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recY94AHjw", "name": "Hema Freeze-dried Strawberries", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recmTW4UcD", "name": "<PERSON><PERSON>s", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reckvWwSkB", "name": "Hema Dried Tangerine Peel Preserved Waxberries", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reccrQZqDr", "name": "<PERSON><PERSON> and <PERSON><PERSON> Dried Fruits", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recayAIc47", "name": "Apple cider vinegar drink", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recjBHeOw4", "name": "<PERSON><PERSON>s Black Plums", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recrDJ0uYK", "name": "<PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recpj0XZfo", "name": "Explosive-juice and plump-fleshed citrus blended fruit juice drink", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recOcYxI0a", "name": "Hema Turkish Dried Apricots", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recwZZ46qA", "name": "<PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recoOLSXNR", "name": "<PERSON><PERSON> Mixed Mushroom Crisps", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec3s7YYjR", "name": "Hema Multicolored Raisins", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recF7r9Gma", "name": "<PERSON><PERSON>ted Fruit Crisps", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recIJ3xsue", "name": "Grape and orange mixed fruit juice", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recVeRiX0Q", "name": "Member's Mark <PERSON> 100% Mulberry Juice", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec896fweK", "name": "Jia <PERSON> 100% NFC Purely Squeezed Kiwi Juice", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recWwtmR6u", "name": "Hema Premium Pineapple Chips", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recU506dMj", "name": "Hema Mixed Vegetable Chips", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recDH6z9A5", "name": "Organic corn juice", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recJi1hDIj", "name": "Prune juice", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recp2Qo1wj", "name": "Hema Open-mouthed Figs", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recMCNnq0e", "name": "Shanzhamannan Original Hawthorn Juice (Fresh Fruit Flavor) Fruit Juice Beverage", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec3<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON> Fresh Steamed Autumn Pear Slices", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdxsMnb0", "name": "A large batch of dried dragon fruit at Hema", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec7v1a0bG", "name": "Hema MAX Japanese Plum Cake", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recvguwCMo", "name": "Hema MAX Daily Mixed Berries", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec0OMzusn", "name": "Hema MAX Chinese Date and Kernel Pie", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recF9vD5HW", "name": "<PERSON><PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recFWP7mzW", "name": "Hema MAX Jackfruit Chips", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recxX1bFID", "name": "Hema MAX Bursting Plum-Wrapped Grapes", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recRl4GNxO", "name": "Hema MAX Prebiotic Coconut Milk and Hawthorn with Liquid Filling", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recMfyQwLv", "name": "Hema Chocolate Pudding", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recQpeGxMo", "name": "Hema MAX Freeze-dried Figs", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reccaok290", "name": "Hema MAX Sweet Pea Crisps", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recqAoB5rQ", "name": "Hema MAX Juicy <PERSON>k-sliced Yellow Peaches", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recMuf65uH", "name": "Hema MAX Freeze-dried Durian", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdqcyUTT", "name": "<PERSON><PERSON>'s Ingeniously Boiled Imperata Cylindrica and Sugar Cane Water", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recIxJS8Rg", "name": "Hema Corn Silk and Brown Rice Tea", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recJxwSmva", "name": "Unif 100% NFC Blueberry Juice", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recZPxUHrL", "name": "Coconut water", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec7PxYDvt", "name": "Unif Oriental Beauty Tea Original Flavor Tea Drink", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recIbhlIKD", "name": "TRADERS DEAL Honey Pomelo Tea", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recyPJxvVd", "name": "Biquan Double Lemon Duck Shit Xiang Lemon Tea", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recNZNyiSO", "name": "Botherless Organic Freeze-dried Strawberries", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recKNr4Xcl", "name": "Coffee milk", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recCpMUBmK", "name": "Sweet pea chips", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recIN9LVYH", "name": "Starbucks Colombian Coffee Latte, Coffee Beverage", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reccyELUiE", "name": "Thai frozen-dried Monthong durian", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recjJ9FUWV", "name": "Dried Summer Music grapes", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recF2Waphj", "name": "Peeled almond kernels at the budding moment", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdO5zbI2", "name": "Orient Leaf Tangerine Pu'er Compound Tea Beverage", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recbfnske2", "name": "Raw Pu-erh tea, sugar-free tea beverage", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recHDX6VMJ", "name": "Suntory Black Oolong Tea, Oolong Tea Beverage (Sugar-Free)", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recANTmgZf", "name": "SINGHA Lion Soda Water", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec8iG09cv", "name": "Watsons Soda Water", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recVtbQRK1", "name": "Schweppes Soda Water", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recK9Jsujt", "name": "BALANCE.TT KETO Beef + Processed Cheese", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recXs2FE00", "name": "Prebiotic Rock-Baked Seaweed", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recCHygMVW", "name": "High-fiber Boletus and Konjac", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recwp932IF", "name": "Egg King Antibiotic-free Quail Eggs", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recSsQc54e", "name": "<PERSON> Crispy Pork Floss Strips", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recE2fWDzJ", "name": "Member's Mark Air-dried Beef", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recZCKDR5s", "name": "Jingqingyuan Traditional Seaweed with Light-salt Olive Oil", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR4Q9tD2g9E", "name": "Xi Zhangji Crunchy Beans", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR4Q9zGx7IO", "name": "<PERSON><PERSON> Bubble-Filled Bubble Gum", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR4QbyLpwwG", "name": "<PERSON>", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR4QbHi4guM", "name": "Liuliumei Clear Plum", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR4QbKck9la", "name": "Liulimei Snow Plums", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR4QbT6hU4F", "name": "Huaweiheng Snowflake Preserved Prunes", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5xtr7N7u0", "name": "Wuweiyuan PK Crispy", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5LuLlijRO", "name": "Seasonal Alliance Grandma's Dishes", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5ShJ8Hv6L", "name": "Golden Rice DIY Graffiti Candies", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recDzp8Tb0", "name": "Daliyuan Black Whole Wheat Mini Bread", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recR2yevyZ", "name": "Daliyuan Milk Peanuts", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recyAnnYrF", "name": "Panpan Daily Fruit and Vegetable Crisps", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuQdrs", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuF2cO", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuhuNI", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuEsaG", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuaH3h", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuYHur", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5Mu6ask", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuZKaY", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuaFXL", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5Muax5m", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recn7j9p5p", "name": "Panpan Coconut Egg Roll Pastries", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}], "manufacturer": [{"productId": "reccAAZZoJ", "name": "Cooked salted duck eggs (FP)", "platform": "大润发", "category": "肉蛋水产", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recSIMFoD4", "name": "Preserved eggs with pine flower flavor", "platform": "大润发", "category": "肉蛋水产", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recpzeK7bM", "name": "Wendefu Glutinous Rice Crackers", "platform": "大润发", "category": "熟食面点", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuq6odXt", "name": "Happy Nuts (Garlic Flavor & Sea Salt Flavor)", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recEY0ZC9N", "name": "Dried apples", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recwiwt7Yo", "name": "Calbee Jagabee Lightly Salted Potato Chips", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recRfLGoB9", "name": "Daily Black Chocolate Freshly Extracted Dark Chocolate Products (Rich Hazelnut Flavor)", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recQtX1bHJ", "name": "100% Coconut water", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdNEzVxJ", "name": "Coconut water", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recnrLbrbJ", "name": "Coconut drink", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recFDJdnDw", "name": "Lotte Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recTQqfDnH", "name": "Mixed nut crunch (assorted nuts)", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reczHsFl5r", "name": "Italian-style mixed nuts (roasted nuts and seed foods)", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recHFaHf45", "name": "High dietary fiber hawthorn sticks", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recqHY5CEk", "name": "Chilean seedless prunes", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recKthAftu", "name": "Oreo Double-flavor Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rect7vhUhm", "name": "<PERSON>i Shadow Play Bee<PERSON> Shreds", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recIKiPiUe", "name": "<PERSON>i Shadow Play Bee<PERSON> Shreds", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recRh9L1FO", "name": "Member's <PERSON> Salted Roasted Cashew Kernels", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recVSB0wYm", "name": "Kerns Original Wood-Fired Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recgF69wR0", "name": "Member's <PERSON>", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdE483m2", "name": "Kernz BBQ-flavored wood chips potato crisps", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recS718l6E", "name": "Exquisite Crispy Mixed Nuts (Light Sea Salt Flavor)", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec3buAaRf", "name": "Bisike Milk-flavored Probiotic Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec9A73HnQ", "name": "Nestle Milo Sports Power Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdTBbS9V", "name": "Oreo Thin & Crispy Vanilla Oreo Crumble Flavor", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recKaHgFKE", "name": "Nestle Milo Sports Power Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recfxckRqV", "name": "Judith Peanut Butter Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reclMbeZ8a", "name": "Lot's Sandwich Crispy Cookies Assorted Flavors Sharing Pack", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recIftGe8L", "name": "Calbee Bisk Probiotics Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recSbZmuKU", "name": "Oreo sandwich cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recflt6vY5", "name": "Oreo sandwich cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reca3z4OH1", "name": "<PERSON><PERSON>'s Sandwich Sticks", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recsvP79tZ", "name": "Oreo Thin Crispy Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recZu6WSa2", "name": "Little <PERSON> Sandwich Bars", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recKYUZutS", "name": "Mach's <PERSON> Rikishi Rock Salt Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rece15XebK", "name": "March New New Rex Rock Salt Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recGY8UhnP", "name": "Crispy Pork Jerky by the Alley", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recWqS1LOm", "name": "Oreo Red Beauty Fragrant Dan-flavored Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reclWjVLdC", "name": "Lotte Bear Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recZN6xHVS", "name": "Lotte Bear Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recWSJj59e", "name": "Golden Cookies Caramel Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reczkoqrAQ", "name": "Xingqi Brand Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec0815ZSw", "name": "Kelaao Vegetable Mini Pancakes", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec1aIYunR", "name": "Jingzai Real Fish! Dried Tofu", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recQEYEtaw", "name": "Jiasili Breakfast Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recjUEryw1", "name": "Coconut Village Spring Coconut Crispy Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recmrgAQ7I", "name": "Haodian High-fiber Digestive Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec2298LYL", "name": "Chandazui Ba Salt-Baked Vegetarian Chicken Tendons", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recYHr6mzl", "name": "Golden Cookies Caramel Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reciwVNnWE", "name": "Laijia Lemon Biscuits in Individual Portions", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recfzzOtxU", "name": "Greek-style strained yogurt", "platform": "大润发", "category": "乳品烘焙", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC1WbR8Jru", "name": "Crispy Pork Jerky by the Alley", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC4Syopzdy", "name": "Jinzai Real Fish! Dried Tofu", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC4SKZozi1", "name": "Jingzai Real Fish-flavored Bean Curd Sticks!", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recbygaD0m", "name": "<PERSON><PERSON><PERSON>t Blueberry Greek Yogurt", "platform": "大润发", "category": "乳品烘焙", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recJGEfmhY", "name": "Daily Freshness Dairy Little Freshness 4.0 Low-Fat Fresh Milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec97uFCVe", "name": "Renyang Yitouniu Pure Milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rectNKntxk", "name": "Glico Pretz Stuffed Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recrrV1HhS", "name": "Taiping Soda Crackers", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec1xETsce", "name": "Taiping Soda Crackers", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recy8GlpmI", "name": "Taiping Soda Crackers", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recprGbkhz", "name": "Taiping Soda Crackers", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recqxQ5XzI", "name": "<PERSON><PERSON><PERSON>tz Filled Biscuits", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recogNn4Lw", "name": "<PERSON><PERSON> Flavored Fermented Milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recDiAlUGm", "name": "QDQ Large - sized Cookie", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recxrwgt37", "name": "Glico Vegetable Crackers", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec3ukzvYa", "name": "WeiLong Spicy Sticks", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recDKwUOGs", "name": "Weilong Kissing Strips", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recNeJaH3P", "name": "Weilong Kissing Strips", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rect5JHEpo", "name": "Weilong Kissing Strips", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recLnEzsXh", "name": "WeiLong Kissing Strips", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec1ZWDDKM", "name": "<PERSON><PERSON> All-round Lactose-free Milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recORqqLTN", "name": "Yili Old-style Yogurt", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recht6neDc", "name": "Yili Yogurt with Big Fruit Bits", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recHoR28B8", "name": "<PERSON><PERSON>at Grain Milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recFWri11K", "name": "<PERSON><PERSON> Golden Series Milk Powder for the Elderly", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recSaKptM2", "name": "<PERSON><PERSON> Powder for the Elderly", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recX2gKyJN", "name": "<PERSON><PERSON> Goat Milk Powder", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recwRiJkV8", "name": "<PERSON><PERSON> - Relieving Dietary Base Milk Powder", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rectnBP19Y", "name": "Yili High Calcium Nutritional Milk Powder for Tall Students", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recKBkwfGi", "name": "Yili Pure Milk Powder", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec1EoSXvB", "name": "<PERSON><PERSON> Lady's Probiotic Fiber Control Milk Powder", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recCYpwDIa", "name": "<PERSON><PERSON>uo Bone Energy Dietary Base Milk Powder", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recDVoUVjG", "name": "<PERSON><PERSON> High-Cal<PERSON>um and Low-Fat Milk Powder for the Elderly", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recoGyWWQM", "name": "Yili Milk Tablets", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recsPMdTBl", "name": "Yili Milk Tablets", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recFnjMJBo", "name": "Yili Milk Tablets", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec7InbkVB", "name": "Yili Palace Cheese Variety Frozen Yogurt", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec<PERSON><PERSON><PERSON>d", "name": "Yili Palace Cheese Variety Creamy Cheese", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec2QN9umI", "name": "<PERSON><PERSON> Children's and Students' Growth Formula Milk Powder", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recySaflDx", "name": "<PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recWVV1b1n", "name": "Hema 100% Coconut Water", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recJ0pfUa9", "name": "<PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec3s7YYjR", "name": "Hema Multicolored Raisins", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recIJ3xsue", "name": "Grape and orange mixed fruit juice", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recVeRiX0Q", "name": "Member's Mark <PERSON> 100% Mulberry Juice", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec5JLla1W", "name": "Hema Organic Fish Skin Peanuts", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recXztLNoy", "name": "Master Kong - <PERSON> Bag Shrimp and Fish Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recpeEYhIF", "name": "Master Kong - Classic Braised Beef Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recU8VOjJY", "name": "<PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec8aSUKq5", "name": "<PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recJi1hDIj", "name": "Prune juice", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recMa56DQh", "name": "<PERSON><PERSON>ed Pine Nuts", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec7v1a0bG", "name": "Hema MAX Japanese Plum Cake", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recHoWCl8w", "name": "Master Kong - Classic Shrimp and Fish Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recYJ5r5c5", "name": "Master Kong - <PERSON> Bag Braised Beef Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reclCfBK96", "name": "Hema MAX Crab-flavored Hawaiian Macadamia Nuts", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recINiwj0S", "name": "Master Kong - Classic Braised Beef Noodles (Packed)", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recTv7HArN", "name": "Master Kong - Super Big Bag Spicy and Refreshing Golden Soup Beef Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reccc2ulWI", "name": "Master Kong - Classic Tomato and Egg Beef Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recSfm3Ozh", "name": "Hema High-protein Air-dried Beef Crisps", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recyfYIfH6", "name": "Master Kong - Classic Mushroom and Chicken Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recjAEjzMe", "name": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec3fu8lkl", "name": "Master Kong - Classic Spicy Beef Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recUezdGrm", "name": "Master Kong - Classic Coriander Spicy Beef Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recj9Gi28E", "name": "Master Kong - Classic Spicy and Sour Beef Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdeRjXWX", "name": "Master Kong - One and a Half Times the Flavor Braised Beef Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reca6jfHeF", "name": "Master Kong - Classic Black Pepper Steak Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recZPxUHrL", "name": "Coconut water", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recIbhlIKD", "name": "TRADERS DEAL Honey Pomelo Tea", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recaS6pMsA", "name": "Hawaiian macadamia nuts with scallop and squid flavor", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reccyELUiE", "name": "Thai frozen-dried Monthong durian", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recANTmgZf", "name": "SINGHA Lion Soda Water", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recCHygMVW", "name": "High-fiber Boletus and Konjac", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuQdrs", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuF2cO", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuhuNI", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuEsaG", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuaH3h", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuYHur", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5Mu6ask", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuZKaY", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuaFXL", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5Muax5m", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRgt3jzFO8d", "name": "Knorr frying powder seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRgtCV79HHq", "name": "Knorr frying powder and coating powder", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRgu9mCSVOl", "name": "<PERSON><PERSON>'s Fry Flour Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}], "specification": [{"productId": "recuq6odXt", "name": "Happy Nuts (Garlic Flavor & Sea Salt Flavor)", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rectNKntxk", "name": "Glico Pretz Stuffed Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec8BDtsX1", "name": "<PERSON><PERSON><PERSON> Pretz Filled Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR4Q93VAS2T", "name": "Xudong Pearl-flavored Tea瓜子", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR4Q9bqkMrS", "name": "Lefang Multi-flavored Roasted Sunflower Seeds", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR4Q9lbueNa", "name": "Master <PERSON>'s <PERSON>", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR4Q9o3t5M5", "name": "<PERSON> Lifang Glutinous Rice Twists", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR4Q9qGThbi", "name": "Miliang Cube Hi! Crispy Corn Chips", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR4Q9tD2g9E", "name": "Xi Zhangji Crunchy Beans", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR4Q9GwO7lm", "name": "<PERSON> on Ni Bear Rice Crackers", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR4QbW8mKTS", "name": "Feiye Ten - Grade Dry - Eating Glutinous Rice Balls", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR4QbZ0NCKR", "name": "Shiziyuan Bursting Stuffed Mochi", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR4Qc5RH57E", "name": "Feiye Ten - Grade Dry - Eating Glutinous Rice Balls", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5oJuZXzl3", "name": "Shiziyuan Bursting Stuffed Mochi", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5oJTZafjs", "name": "Shiziyuan Bursting Stuffed Mochi", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5oJXEuX8m", "name": "Zhihua Garden Mung Bean Cake", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5oK1UNwJw", "name": "<PERSON>g Ni Sesame Sticks", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5oK59RRCK", "name": "<PERSON>ci Time Fine Hawthorn Round Slices", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5oK8FzMQs", "name": "Xiaoci Time Original Flavor Hawthorn Slices", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5oKtkHJ62", "name": "Yuqi Peanut Nougat", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5oLb0Ctzu", "name": "Bangbang Milk Cookies", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5xsNVPJG1", "name": "What a coincidence cup\n\n注：由于“真巧杯”可能是特定品牌名，如果它本身有官方英文表述，以上翻译可能不准确，需以官方为准。", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5xsYnx5xj", "name": "Xiaoshizui Garlic-flavored Green Peas", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5xtc1HhdM", "name": "<PERSON><PERSON><PERSON>", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5xthMZ3s8", "name": "<PERSON> Twisted Dough Sticks", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5xto2k0as", "name": "Bald-headed kid, a green onion.", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5xtr7N7u0", "name": "Wuweiyuan PK Crispy", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5xtA0BcaE", "name": "<PERSON> Strawberry Yogurt Hawthorn Balls", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5FRPOGXqj", "name": "Always remember <PERSON> Crispy", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5FRSBkQzD", "name": "Always remember <PERSON> Crispy.", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5FRUKuTmB", "name": "Always remember Zhangxin Crispy Noodles", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5FRXoo6yb", "name": "Baishiyuan Ramen Balls", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5Lv53FwfW", "name": "Couple's <PERSON>ng <PERSON> Cake", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5Lv8emJtP", "name": "Husband-and-wife wife cakes", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5LvaIP2gH", "name": "<PERSON><PERSON>py <PERSON>", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5LvjCtMvf", "name": "Weimeixin Milk Protein Bar Bread", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5NUCirNbO", "name": "Beining Pineapple Big Pocket", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5NUFvGkI5", "name": "<PERSON><PERSON><PERSON> helps <PERSON><PERSON>.", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5NUIx2Dvp", "name": "<PERSON><PERSON><PERSON> Love Boundless Toast", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5NUStZaNr", "name": "Duozuimao Butterfly Coconut Buns", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5NUVlXnlu", "name": "A1 Snack Research Institute Watermelon-flavored Sandwich Toast", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5NV0MQqyz", "name": "New Wheat Wave Black Chocolate Fresh Milk Thick Toast", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5NV3cqRnF", "name": "Tongxiang Morning Bell Steamed Cake", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5NV68nW0l", "name": "Gangrong Food Steamed Cake", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5NVbokmvZ", "name": "Hai Zhizui Fragrant Rice Cake", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5RcTO2tVC", "name": "<PERSON>", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5Rd9O0cHT", "name": "Youngster Hengha Chocolate-flavored Croissant", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5Rdia4vwc", "name": "Gram sandwich bread", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5Rdpg3jUO", "name": "<PERSON><PERSON><PERSON> Nut Bread Sticks", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5RdvZTYXG", "name": "Jinsheng Premium Tiger Skin Cake", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5RdBYyLP9", "name": "Little General Hum-Ha Happy Egg Waffle Cake", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuQdrs", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuF2cO", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuhuNI", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuEsaG", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuaH3h", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuYHur", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5Mu6ask", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuZKaY", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuaFXL", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5Muax5m", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}], "origin.city": [{"productId": "recs6Ss5Nt", "name": "Salt-baked flavored pistachios", "platform": "胖东来", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recXSrclkb", "name": "Original-flavored pistachios", "platform": "胖东来", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuq6odXt", "name": "Happy Nuts (Garlic Flavor & Sea Salt Flavor)", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recwiwt7Yo", "name": "Calbee Jagabee Lightly Salted Potato Chips", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recQtX1bHJ", "name": "100% Coconut water", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdNEzVxJ", "name": "Coconut water", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recnrLbrbJ", "name": "Coconut drink", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recEPv0sT6", "name": "Dried red dragon fruit", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recFDJdnDw", "name": "Lotte Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recTQqfDnH", "name": "Mixed nut crunch (assorted nuts)", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reczHsFl5r", "name": "Italian-style mixed nuts (roasted nuts and seed foods)", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reczFGpNPF", "name": "Taifeng Spiced <PERSON><PERSON>", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recqHY5CEk", "name": "Chilean seedless prunes", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recKthAftu", "name": "Oreo Double-flavor Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recaXwl6qW", "name": "Member's <PERSON><PERSON>", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recS5oC1J8", "name": "Apple and Astragalus Drink", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recRh9L1FO", "name": "Member's <PERSON> Salted Roasted Cashew Kernels", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recVSB0wYm", "name": "Kerns Original Wood-Fired Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recgF69wR0", "name": "Member's <PERSON>", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdE483m2", "name": "Kernz BBQ-flavored wood chips potato crisps", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recS718l6E", "name": "Exquisite Crispy Mixed Nuts (Light Sea Salt Flavor)", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec3buAaRf", "name": "Bisike Milk-flavored Probiotic Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec9A73HnQ", "name": "Nestle Milo Sports Power Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdTBbS9V", "name": "Oreo Thin & Crispy Vanilla Oreo Crumble Flavor", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recKaHgFKE", "name": "Nestle Milo Sports Power Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recfxckRqV", "name": "Judith Peanut Butter Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reclMbeZ8a", "name": "Lot's Sandwich Crispy Cookies Assorted Flavors Sharing Pack", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recIftGe8L", "name": "Calbee Bisk Probiotics Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recSbZmuKU", "name": "Oreo sandwich cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recflt6vY5", "name": "Oreo sandwich cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recsvP79tZ", "name": "Oreo Thin Crispy Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recKYUZutS", "name": "Mach's <PERSON> Rikishi Rock Salt Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rece15XebK", "name": "March New New Rex Rock Salt Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec1kEUYEi", "name": "<PERSON>wich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recWqS1LOm", "name": "Oreo Red Beauty Fragrant Dan-flavored Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recU9qtSVU", "name": "<PERSON> Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reclWjVLdC", "name": "Lotte Bear Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recZN6xHVS", "name": "Lotte Bear Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recWSJj59e", "name": "Golden Cookies Caramel Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reczkoqrAQ", "name": "Xingqi Brand Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec0815ZSw", "name": "Kelaao Vegetable Mini Pancakes", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec1aIYunR", "name": "Jingzai Real Fish! Dried Tofu", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recQEYEtaw", "name": "Jiasili Breakfast Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recmrgAQ7I", "name": "Haodian High-fiber Digestive Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recYHr6mzl", "name": "Golden Cookies Caramel Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reciwVNnWE", "name": "Laijia Lemon Biscuits in Individual Portions", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recfzzOtxU", "name": "Greek-style strained yogurt", "platform": "大润发", "category": "乳品烘焙", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC4Syopzdy", "name": "Jinzai Real Fish! Dried Tofu", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC4SKZozi1", "name": "Jingzai Real Fish-flavored Bean Curd Sticks!", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recbygaD0m", "name": "<PERSON><PERSON><PERSON>t Blueberry Greek Yogurt", "platform": "大润发", "category": "乳品烘焙", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recYoO1tOg", "name": "Yili Changqing Yogurt Probiotics", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recvfAtGca", "name": "Yili Changqing Yogurt Probiotics", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recDKwUOGs", "name": "Weilong Kissing Strips", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recNeJaH3P", "name": "Weilong Kissing Strips", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rect5JHEpo", "name": "Weilong Kissing Strips", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recLnEzsXh", "name": "WeiLong Kissing Strips", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recgmsOPHd", "name": "<PERSON><PERSON>oal-Burned Probiotic Yogurt", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recb1MgZip", "name": "<PERSON><PERSON>tein Time Yogurt", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recneAhDLU", "name": "<PERSON><PERSON>tein Time Yogurt", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recORqqLTN", "name": "Yili Old-style Yogurt", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recht6neDc", "name": "Yili Yogurt with Big Fruit Bits", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recWVV1b1n", "name": "Hema 100% Coconut Water", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recIJ3xsue", "name": "Grape and orange mixed fruit juice", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recVeRiX0Q", "name": "Member's Mark <PERSON> 100% Mulberry Juice", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recEBxcYrW", "name": "Hema Fresh Coconut Meat Freshly Pressed Thick Coconut Milk", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recJi1hDIj", "name": "Prune juice", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recXkkdK7J", "name": "COFCO Fulinmen High-quality Northeast Rice", "platform": "天猫旗舰店", "category": "大米", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recLzxYi12", "name": "COFCO Fulinmen KING FOOD Royal Mary Thai Imported Jasmine Rice", "platform": "天猫旗舰店", "category": "大米", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec2VX33k4", "name": "<PERSON><PERSON> Pink Coconut Electrolyte Water", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recZPxUHrL", "name": "Coconut water", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recIbhlIKD", "name": "TRADERS DEAL Honey Pomelo Tea", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recaS6pMsA", "name": "Hawaiian macadamia nuts with scallop and squid flavor", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reccyELUiE", "name": "Thai frozen-dried Monthong durian", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recANTmgZf", "name": "SINGHA Lion Soda Water", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec8iG09cv", "name": "Watsons Soda Water", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recCHygMVW", "name": "High-fiber Boletus and Konjac", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5FSTbKgjD", "name": "<PERSON><PERSON><PERSON>", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5FT32XeTk", "name": "<PERSON><PERSON><PERSON>", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5RdvZTYXG", "name": "Jinsheng Premium Tiger Skin Cake", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuQdrs", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuF2cO", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuhuNI", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuEsaG", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuaH3h", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuYHur", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5Mu6ask", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuZKaY", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuaFXL", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5Muax5m", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRgt3jzFO8d", "name": "Knorr frying powder seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRgtCV79HHq", "name": "Knorr frying powder and coating powder", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRgu9mCSVOl", "name": "<PERSON><PERSON>'s Fry Flour Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}], "barcode": [{"productId": "rec7tDisnM", "name": "100% Tomato Composite Fruit and Vegetable Juice", "platform": "胖东来", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reck4P1wuD", "name": "100% Mango Compound Fruit Juice", "platform": "胖东来", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recJzvD22F", "name": "100% Grape Compound Fruit Juice", "platform": "胖东来", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recHfjaZVt", "name": "100% Orange Juice", "platform": "胖东来", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recs6Ss5Nt", "name": "Salt-baked flavored pistachios", "platform": "胖东来", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recsJ5rkcN", "name": "Original flavored cashew nuts", "platform": "胖东来", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recXSrclkb", "name": "Original-flavored pistachios", "platform": "胖东来", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reccAAZZoJ", "name": "Cooked salted duck eggs (FP)", "platform": "大润发", "category": "肉蛋水产", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recSIMFoD4", "name": "Preserved eggs with pine flower flavor", "platform": "大润发", "category": "肉蛋水产", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recYQk5G2F", "name": "Gift box of multi-flavor oatmeal crisps", "platform": "胖东来", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recvG1B30k", "name": "Xiangbalao marinated eggs", "platform": "大润发", "category": "肉蛋水产", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec0YwvtNO", "name": "Strawberry fruit oat crunch", "platform": "胖东来", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recRMRfFnM", "name": "Aishang Mimi Shrimp-flavored Puffed Food", "platform": "猫超", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec07zrMTR", "name": "Xiangbalao Honey-flavored Eggs", "platform": "大润发", "category": "肉蛋水产", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recOe2LdPS", "name": "Daily nut oat crunch", "platform": "胖东来", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recScO4NfJ", "name": "Yogurt fruit and crispy oats", "platform": "胖东来", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recqwTBJWi", "name": "Nutty Russian bread", "platform": "胖东来", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recpzeK7bM", "name": "Wendefu Glutinous Rice Crackers", "platform": "大润发", "category": "熟食面点", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recDh6bSFI", "name": "Dried silver perch", "platform": "胖东来", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recDEmzvKG", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Coconut and Job's Tears <PERSON><PERSON>", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recWAz2u2H", "name": "Dried cod", "platform": "胖东来", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdWrGlHP", "name": "Spicy beef strips", "platform": "胖东来", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec9NEnm3Z", "name": "Combination of taro paste and mochi crispy cakes and red bean and salted egg yolk crispy cakes", "platform": "胖东来", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recMF6aEEt", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recZEAWWB1", "name": "<PERSON><PERSON> Instant Freshly Stewed Five-Red Congee for a Full-of-Vitality Meal", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recDpw51WS", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recusdDm8K", "name": "Pressed first-grade peanut oil", "platform": "胖东来", "category": "粮油干调", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recWSuLZNV", "name": "Sunflower oil with zero trans fat", "platform": "胖东来", "category": "粮油干调", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec1nODxhc", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec189xRcX", "name": "<PERSON><PERSON>, <PERSON> and Tremella <PERSON>gee", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recshF3soF", "name": "Sesame paste made from small grinders", "platform": "胖东来", "category": "粮油干调", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recMsrroqE", "name": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> Instant Porridge - Private Style Beef Porridge", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recZ3PbbAi", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQpS4seM1sE", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQpSxaSZoPd", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQpSxpLRk66", "name": "Lay's potato chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQpSxFnJxxt", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recQSrpcqc", "name": "Green grape milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQpUF9Y0XXw", "name": "Lay's potato chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQpUFoKzIow", "name": "Lay's potato chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQpV72tJDjP", "name": "Lay's potato chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQpV7hvUbqh", "name": "Lay's potato chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recajkRsJW", "name": "Honeydew milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recp7Q9koo", "name": "Lay's potato chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recqggqZOr", "name": "High-protein meat jerky", "platform": "猫超", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recpTluCMC", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rechCyOhs8", "name": "Liangpinpuzi Carbon-grilled Beef <PERSON> (Roasted Meat)", "platform": "猫超", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recxeBLvjI", "name": "Oishi Garden Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recjKpZaZX", "name": "Laiyifen High-Protein Natural Dried Meat Slices", "platform": "猫超", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQq3oGcTsbJ", "name": "Oishi Country Style Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQq3oYPPov8", "name": "Oishi Garden Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQq3xpZ0LH3", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuq6odXt", "name": "Happy Nuts (Garlic Flavor & Sea Salt Flavor)", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recEY0ZC9N", "name": "Dried apples", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recwiwt7Yo", "name": "Calbee Jagabee Lightly Salted Potato Chips", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recRfLGoB9", "name": "Daily Black Chocolate Freshly Extracted Dark Chocolate Products (Rich Hazelnut Flavor)", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recQtX1bHJ", "name": "100% Coconut water", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recPcj2J2z", "name": "<PERSON>ofeng Free Love (Baijiu)", "platform": "胖东来", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recZKuhdMR", "name": "Craft black beer", "platform": "胖东来", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recfgjPAKS", "name": "Craft wheat beer", "platform": "胖东来", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdNEzVxJ", "name": "Coconut water", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rechixM3PZ", "name": "Craft wheat beer", "platform": "胖东来", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recTGdUQ8q", "name": "Dried conger eel", "platform": "胖东来", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recswJFWrN", "name": "Dried anglerfish", "platform": "胖东来", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recyTJMNtJ", "name": "Dried cod fish, spicy flavor", "platform": "胖东来", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recnrLbrbJ", "name": "Coconut drink", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recmHvp5XE", "name": "Lvsheng Spicy Beef", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec03L0cs9", "name": "Freshly extracted freeze-dried hazelnuts", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recjulYPLB", "name": "Jiaguoyuan 100% NFC Black Diamond Pineapple Juice", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recvE8MmK8", "name": "<PERSON><PERSON><PERSON><PERSON>", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recRPLG1Yf", "name": "Pine mushroom crispy sunflower kernels", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rechYPCOY4", "name": "<PERSON><PERSON><PERSON>ng Secret Beef with <PERSON><PERSON>", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec44iYdJE", "name": "YangShengTang Birch Tree Sap", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recybVoM3e", "name": "Huangji Hazelnut and Peanut Butter Sandwich Crackers", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recFDJdnDw", "name": "Lotte Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recTQqfDnH", "name": "Mixed nut crunch (assorted nuts)", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reckmv9CK1", "name": "Hormel Light & Thin Bee<PERSON>", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reczHsFl5r", "name": "Italian-style mixed nuts (roasted nuts and seed foods)", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rechgC5KR5", "name": "Heytea Kale and Cucumber Compound Fruit and Vegetable Juice Drink", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reczFGpNPF", "name": "Taifeng Spiced <PERSON><PERSON>", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recXL3HKiL", "name": "Oreo Assorted Flavors Gift Box", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recHFaHf45", "name": "High dietary fiber hawthorn sticks", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recDiRlHdz", "name": "Beichuwe Angus Steak Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recT0Doo2W", "name": "Oreo Original Cream Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recqHY5CEk", "name": "Chilean seedless prunes", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recgPbHy8z", "name": "Jiale Spicy Seafood Dipping and Cooking Sauce Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recJQRsxg1", "name": "<PERSON>'s <PERSON>f Sticks", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recX01KmS0", "name": "<PERSON><PERSON><PERSON>ly-<PERSON><PERSON> Compound Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuS8QLH2", "name": "<PERSON>'s <PERSON>f Sticks", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recBRh3Vyy", "name": "Refined Sweet Potato Sticks", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recKthAftu", "name": "Oreo Double-flavor Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQqVUzB31br", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rect7vhUhm", "name": "<PERSON>i Shadow Play Bee<PERSON> Shreds", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recIKiPiUe", "name": "<PERSON>i Shadow Play Bee<PERSON> Shreds", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recAgc4T9s", "name": "Daily nuts (nuts + dried fruits)", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec3HNKv7O", "name": "VC Polyphenol Hawthorn Strips", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recPw3sGe9", "name": "Organic fruit juice, dried blueberries", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdfT1Kkp", "name": "<PERSON> Yao Sweet Potato Sticks", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recg5Y8wOX", "name": "Member's <PERSON>-flavored Hawaiian Macadamia Nuts", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recaXwl6qW", "name": "Member's <PERSON><PERSON>", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recJ0sfa20", "name": "<PERSON><PERSON> Small Dried Mandarins", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recexGxJNE", "name": "Organic savory peanuts", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recS5oC1J8", "name": "Apple and Astragalus Drink", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recs5UGvHZ", "name": "<PERSON><PERSON> Original Flavor Big Sunflower Seeds", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recRh9L1FO", "name": "Member's <PERSON> Salted Roasted Cashew Kernels", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recsUm9xY1", "name": "Hangzhou-style pecan kernels", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recVSB0wYm", "name": "Kerns Original Wood-Fired Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recgF69wR0", "name": "Member's <PERSON>", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec07vQf9y", "name": "Matsutake Organic Fish Skin Peanuts (Coated Peanuts)", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recEE6E3C2", "name": "Dole Turkish Dried Apricots", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQrg3G9AUU7", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdE483m2", "name": "Kernz BBQ-flavored wood chips potato crisps", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recXcG3YSt", "name": "Organic freeze-dried strawberries", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQrhcxGUCuf", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recbze5e6N", "name": "Organic chestnut kernels", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec7LAQJ7s", "name": "Snow pear and loquat syrup", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recS718l6E", "name": "Exquisite Crispy Mixed Nuts (Light Sea Salt Flavor)", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQrknXzaZ0M", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQrkojWG1UA", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQrkonjlTye", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQrkotzdK7h", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQrkowPlBUf", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQrkqvFVKjZ", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQrkqC74VPf", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQrkqH1UcKG", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQrkqJmYjUw", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQrkqOFUZ2Q", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQrkqQXuEe1", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQrkqUnqMFd", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQrkqWxP9AE", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQrkr1TA24O", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recXpiBoSN", "name": "Huaweixiang Barbecue Flavored Beef <PERSON>", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec09KxZwA", "name": "Oreo Classic Original Flavor Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec3buAaRf", "name": "Bisike Milk-flavored Probiotic Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recYSeYzJc", "name": "Small <PERSON>li <PERSON> (Satay Flavor)", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec9A73HnQ", "name": "Nestle Milo Sports Power Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recCdxxJiI", "name": "Mother's original sliced beef", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdgBf9sQ", "name": "Mom's Original Sliced Beef", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recEcMWYOE", "name": "Master Kong 3+2 Soda Sandwich Biscuits (Lemon Flavor)", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recd75yD1y", "name": "<PERSON><PERSON> Slabs", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recpoJb1Hg", "name": "Master Kong 3+2 Soda Sandwich Cookies (Cream Flavor)", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdTBbS9V", "name": "Oreo Thin & Crispy Vanilla Oreo Crumble Flavor", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recbxp5b6k", "name": "Oreo Mini Hazelnut Chocolate-Flavored Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recKaHgFKE", "name": "Nestle Milo Sports Power Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reclHOCIBY", "name": "Natural Style Honey-flavored Beef Jerky", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recq<PERSON>na<PERSON>eh", "name": "Three Squirrels S<PERSON><PERSON>", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recfxckRqV", "name": "Judith Peanut Butter Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reckK0ABEr", "name": "Small chili beef jerky", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recr34JeBP", "name": "<PERSON><PERSON><PERSON><PERSON>", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reczZTtHCL", "name": "Old Sichuan Beef Cubes", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recOC8o17P", "name": "<PERSON><PERSON>", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reclMbeZ8a", "name": "Lot's Sandwich Crispy Cookies Assorted Flavors Sharing Pack", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recbwq13kI", "name": "<PERSON>'s <PERSON>f Sticks", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recIftGe8L", "name": "Calbee Bisk Probiotics Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recSbZmuKU", "name": "Oreo sandwich cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recflt6vY5", "name": "Oreo sandwich cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recAQee5Bo", "name": "Master Kong 3+2 Soda Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recYfqYd5u", "name": "Master Kong 3+2 Soda Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuvHQXhK", "name": "Chocolate milk drink", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rechIWUUtt", "name": "<PERSON><PERSON> Dried Meat Slices", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recoXlqqSF", "name": "However, their Sanyang dried meat slices.", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reca3z4OH1", "name": "<PERSON><PERSON>'s Sandwich Sticks", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recsvP79tZ", "name": "Oreo Thin Crispy Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recmuKD1Ga", "name": "Youyou Wild Pepper Pig Skin Crystals", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recLZaX6e0", "name": "Succulent Juicy Grapefruit Mixed Fruit Juice Drink", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recZu6WSa2", "name": "Little <PERSON> Sandwich Bars", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recKYUZutS", "name": "Mach's <PERSON> Rikishi Rock Salt Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rece15XebK", "name": "March New New Rex Rock Salt Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec8DhdvUG", "name": "Bestore Sichuan-flavored Pork Slices", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recGY8UhnP", "name": "Crispy Pork Jerky by the Alley", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec1kEUYEi", "name": "<PERSON>wich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reczQVsEfv", "name": "Lifeng Pork Jerky", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recWqS1LOm", "name": "Oreo Red Beauty Fragrant Dan-flavored Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recHzRpcoP", "name": "Uncle <PERSON><PERSON><PERSON>'s Pork Jerky", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recU9qtSVU", "name": "<PERSON> Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec7auo3fM", "name": "100% NFC Orange Juice", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recyyDn25j", "name": "Jiasili Fruit-flavored Filled Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec2BDm2Ld", "name": "Jiasili Fruit-flavored Filled Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec58iffAB", "name": "Jiasili Fruit-flavored Sandwich Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recWSv9q5T", "name": "Saramy Roasted Pork Slices", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recoc4i14B", "name": "Oreo Zero-Sugar Crispy Thin Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reclWjVLdC", "name": "Lotte Bear Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recZN6xHVS", "name": "Lotte Bear Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recSD1mC6N", "name": "Master Kong Sweet and Crispy Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recepyKz0Z", "name": "Hormel Miniature Roasted Meat", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recBDRwwCm", "name": "Xinda Authentic Meat Sausages", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recpHnLE0C", "name": "<PERSON><PERSON> Crunchy <PERSON>od <PERSON>wich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recS5cJBqN", "name": "<PERSON><PERSON> Crunchy <PERSON>od <PERSON>wich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec5VqTHCj", "name": "Nut milk plant protein beverage", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recWSJj59e", "name": "Golden Cookies Caramel Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reczkoqrAQ", "name": "Xingqi Brand Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recwsg2G8T", "name": "Saiweiuan Sandwich Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recJz8A4iQ", "name": "Saiwei Hall Black Sugar Malt Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recHL89yWt", "name": "Original cut French fries", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recFRVvzzm", "name": "Haochidian Crispy Almond Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec9BaLaX4", "name": "<PERSON><PERSON><PERSON> Walnut Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recvhEGNDt", "name": "Original cut French fries", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recxmDnTuJ", "name": "Haochidian Crispy Cashew Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recvJ47BgP", "name": "Freeze-dried lemon slices with honey", "platform": "大润发", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recZGkGoPK", "name": "Jiayou Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recfBWh3GP", "name": "Jiayou Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdfM4ptQ", "name": "Zuming Jiangnan Spicy-flavored Bean Curd Sticks", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recfvfF1zs", "name": "Xindachang Braised Dried Tofu in Sauce", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec0815ZSw", "name": "Kelaao Vegetable Mini Pancakes", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec1aIYunR", "name": "Jingzai Real Fish! Dried Tofu", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recQEYEtaw", "name": "Jiasili Breakfast Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recITHlNBE", "name": "Zuming Stinky Tofu", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recEjEpbdd", "name": "Zuming Fragrant Bean Rolls", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recjUEryw1", "name": "Coconut Village Spring Coconut Crispy Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recaPZ7BoT", "name": "Taikang Big Milk Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recMEls6Kx", "name": "Jinjin Braised To<PERSON> Cubes in Sauce", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec5qcHts8", "name": "Jinjin Braised Dr<PERSON> Tofu in Sauce", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recmrgAQ7I", "name": "Haodian High-fiber Digestive Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec2298LYL", "name": "Chandazui Ba Salt-Baked Vegetarian Chicken Tendons", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec2CQdgxX", "name": "<PERSON>u Egg Yolk Pancakes", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recZB1XBa2", "name": "<PERSON> Salted Egg Yolk Pancakes", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recfMF8RQ6", "name": "<PERSON><PERSON><PERSON><PERSON> Tofu in Dipping Sauce", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recYHr6mzl", "name": "Golden Cookies Caramel Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recMJVOy27", "name": "<PERSON><PERSON><PERSON> and <PERSON>rispy Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec7xM6T5w", "name": "Jiangzhong Dietary Therapy Red Adzuki Bean and Poria Cocos Thin Crispy Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec0kPCWQH", "name": "<PERSON><PERSON><PERSON> and <PERSON>s", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recVfo8FKN", "name": "Jiangzhong Hericium Erinaceus Rice Porridge Crispy Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recmtN7OSo", "name": "<PERSON><PERSON><PERSON> Pretz Filled Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recJjcf5YD", "name": "Baoleyou Milk and Calcium Digital Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recExabjcl", "name": "Luding Coffee Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recYiyVgYi", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQBHOngQel2", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reciwVNnWE", "name": "Laijia Lemon Biscuits in Individual Portions", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recUgNQgng", "name": "Glico Pretz Stuffed Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recedePkt4", "name": "Glico Pretz Stuffed Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recm8EyECY", "name": "<PERSON><PERSON><PERSON>u Chewy Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recVbwpGps", "name": "<PERSON><PERSON><PERSON> Pretz Filled Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recQtZ2mY3", "name": "Xianqu Decorated Cookies", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recbhJupF8", "name": "<PERSON><PERSON><PERSON> Pretz Filled Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recLdZ8bKx", "name": "Milk skin traditional yogurt", "platform": "大润发", "category": "乳品烘焙", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec5sYwfGA", "name": "Organic large oat flakes", "platform": "大润发", "category": "麦片", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recOIG01mS", "name": "Baixiang - More than half a bag of Spicy Beef Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recfzzOtxU", "name": "Greek-style strained yogurt", "platform": "大润发", "category": "乳品烘焙", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reclCdhzGg", "name": "White Elephant - Two-generation Braised Beef Noodles with Nearly One More Bag of Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQBXgDF9wID", "name": "<PERSON>'s <PERSON><PERSON>", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recXFfv9CW", "name": "<PERSON><PERSON><PERSON> Pretz Filled Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec4Uf4jxm", "name": "<PERSON><PERSON><PERSON> Pretz Filled Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recQhaeWB9", "name": "<PERSON> - Classic Braised Beef Noodles (Bulk Pack of 5)", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC1WbR8Jru", "name": "Crispy Pork Jerky by the Alley", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC28wOLIAq", "name": "<PERSON><PERSON><PERSON>tz Filled Biscuits", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC2NNeZvNB", "name": "<PERSON><PERSON><PERSON>tz Filled Biscuits", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC3bU16VE2", "name": "<PERSON><PERSON><PERSON> Pretz Filled Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC3ACBkrqi", "name": "Piaoling Uncle's Pork Jerky", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recVI8n7yP", "name": "White Elephant - New Classic Extra Large Bag Braised Beef Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recbOtd8Lt", "name": "Peach Oolong Beer", "platform": "大润发", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC3YqgyN6b", "name": "<PERSON><PERSON><PERSON>ted Dried Tofu", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC4b5SAa8Q", "name": "Zuming Stinky Tofu", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recKtDbVAs", "name": "Glico Pretz Stuffed Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reczVSXR0Y", "name": "Maojian Green Tea Beer", "platform": "大润发", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC4Syopzdy", "name": "Jinzai Real Fish! Dried Tofu", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC4SKZozi1", "name": "Jingzai Real Fish-flavored Bean Curd Sticks!", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC55gOcTCC", "name": "<PERSON><PERSON><PERSON> Pretz Filled Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rechcKoDxd", "name": "White Elephant - Extra-large Bag of Old Pickled Cabbage Beef Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC5h4BjbPD", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC5h4BnGMK", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC5h4BdDl5", "name": "Lay's potato chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC5h4B1y85", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC5h4B6sRc", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC5h4B5XpR", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC5h4BKDzP", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC5h4BDlqM", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC5h4B3KYH", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC5h4BhhiF", "name": "Xindachang Marinated Dried Tofu", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recbygaD0m", "name": "<PERSON><PERSON><PERSON>t Blueberry Greek Yogurt", "platform": "大润发", "category": "乳品烘焙", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC5EYZWMmY", "name": "Hormel Mini Roasted Meats", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC67gYB2Js", "name": "<PERSON><PERSON><PERSON> Pretz Filled Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recrGd2Dsy", "name": "Chilean See<PERSON><PERSON>run<PERSON>", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC6ARfMTqT", "name": "Glico Pretz Stuffed Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC6XMz6da0", "name": "Glico Pretz Stuffed Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recyYjTszA", "name": "White Elephant - Extra-large Bag of Spicy Beef Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC7n6Vc5K2", "name": "<PERSON><PERSON><PERSON> Pretz Filled Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC86VD68dX", "name": "<PERSON><PERSON><PERSON> Pretz Filled Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recOGf7NkT", "name": "Shandong Sweet Potato Noodles", "platform": "大润发", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC8mlNVa6V", "name": "<PERSON><PERSON><PERSON> Pretz Filled Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec9ymrjDe", "name": "White Elephant - Second Generation Braised Beef Noodles with Nearly Half a Bag More Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC8FGmnbJ2", "name": "<PERSON><PERSON><PERSON> Pretz Filled Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recJGEfmhY", "name": "Daily Freshness Dairy Little Freshness 4.0 Low-Fat Fresh Milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC8XW8WHIo", "name": "<PERSON><PERSON><PERSON> Pretz Filled Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recH8M8tTt", "name": "Guanshengyuan Compressed Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recEeVaeAx", "name": "Guanshengyuan Compressed Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recGbfLPWo", "name": "Guanshengyuan Compressed Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec97uFCVe", "name": "Renyang Yitouniu Pure Milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recxVWvI5d", "name": "Rice from the cold regions of Northeast China", "platform": "大润发", "category": "粮油干调", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC9O6VvIeR", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC9OHPYZxA", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC9P8FWVK0", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC9PvSsoHr", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC9PSBnB8q", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC9QAB4nj8", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC9R01dtWT", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQC9RoV3PyK", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recAqEukOK", "name": "Cold-brewed Lemon Boneless Chicken Feet", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recV0fGzJH", "name": "Baixiang - Delicious Spicy Beef Noodles in Soup", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCbd2MdtHC", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCbvAalxmm", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCbHskVTDO", "name": "<PERSON><PERSON><PERSON> Pretz Stuffed Biscuits", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec8B0Mxex", "name": "Black Gold Rice Crackers", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCcfXpoZ6l", "name": "<PERSON><PERSON><PERSON>tz Fill Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec9NcTmNh", "name": "Fu'ercinqiu Plum Crispy Thin Biscuits", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recliTcrMU", "name": "Fu'er Love Letter Plum Crispy Thin Crackers", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recs1WKL4z", "name": "BaiXiang - Dajiaojiao Classic Turkey Flavored Noodles (Cold Mix)", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recke6g2i1", "name": "Low-sugar Coconut and Nut Crisps", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reclHOSq0K", "name": "<PERSON><PERSON><PERSON> Pretz Filled Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recqkV4nBD", "name": "<PERSON><PERSON> and <PERSON><PERSON><PERSON> Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recF6LURsI", "name": "<PERSON><PERSON>", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCdTs6cAR4", "name": "<PERSON><PERSON><PERSON> Pretz Filled Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recaxBHg5y", "name": "Wasabi-flavored Hawaiian nuts", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCejpry6ly", "name": "<PERSON><PERSON><PERSON>tz Filled Biscuits", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recEclFj8b", "name": "BaiXiang- Spicy Crayfish Mixed Noodles of Dajiaojiao", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rectNKntxk", "name": "Glico Pretz Stuffed Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCfjPebXuR", "name": "<PERSON><PERSON><PERSON>tz Filled Biscuits", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reczNNMLe5", "name": "Spiced Braised <PERSON>", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recW4NacAA", "name": "BaiXiang - <PERSON><PERSON> Original Flavor Chicken Noodle Soup", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recrrV1HhS", "name": "Taiping Soda Crackers", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec1xETsce", "name": "Taiping Soda Crackers", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recy8GlpmI", "name": "Taiping Soda Crackers", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recprGbkhz", "name": "Taiping Soda Crackers", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec8BDtsX1", "name": "<PERSON><PERSON><PERSON> Pretz Filled Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reck5oRyxk", "name": "Lay's Big Wave Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recTCCAr3V", "name": "Lay's Big Wave Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recsbri2Mv", "name": "Lay's Wavy Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recG2g99Rd", "name": "Lay's Wavy Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec1eR73Yy", "name": "Lay's Wavy Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recimfTxor", "name": "Lay's Wavy Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recyh<PERSON>oer", "name": "Low-fat and high-protein chicken breast sausage (sea salt and black pepper)", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recWPaA2DQ", "name": "White Elephant - Almost a whole bag of Golden Soup Fatty Beef Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recduwcQbw", "name": "<PERSON>li <PERSON> Probiotic Flavored Fermented Milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recBhKGL2Q", "name": "<PERSON>li <PERSON> Probiotic Flavored Fermented Milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec5<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>li <PERSON> Probiotic Flavored Fermented Milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec7q6sUwz", "name": "<PERSON>li <PERSON> Probiotic Flavored Fermented Milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec4XUldqN", "name": "<PERSON>li <PERSON> Probiotic Flavored Fermented Milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recwmNhDRx", "name": "<PERSON><PERSON><PERSON> Hawthorn Juice", "platform": "大润发", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recqxQ5XzI", "name": "<PERSON><PERSON><PERSON>tz Filled Biscuits", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reclZYBP6i", "name": "Low-sugar Amber Walnuts", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recgm3TzN6", "name": "Lay's Wavy Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recgkkjNTG", "name": "Lay's Big Wave Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec4SnykXq", "name": "Lay's Big Wave Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reczSYBg8L", "name": "Lay's Wavy Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reckm4nG3j", "name": "Lay's Wavy Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rechhJHnFe", "name": "Lay's Wavy Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCjcksz81h", "name": "Lay's Wavy Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec6YrusrY", "name": "Yili High-Calcium Fresh Milk Pasteurized Milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reczERV3bk", "name": "Black truffle and ham soda crackers", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recw<PERSON><PERSON><PERSON><PERSON>", "name": "Spiced Braised Duck <PERSON>", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reciffOraI", "name": "<PERSON><PERSON> Live Bacteria Type Lactic Acid Bacteria", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec4W819uj", "name": "Chips Ahoy! Soft Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recry6f4aX", "name": "Pecan nuts", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recJ1b83pK", "name": "100% NFC Freshly Squeezed Orange Juice with Rock Sugar", "platform": "大润发", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recogNn4Lw", "name": "<PERSON><PERSON> Flavored Fermented Milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rechQNmlzf", "name": "<PERSON><PERSON><PERSON>tz Filled Biscuits", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recraOhlkL", "name": "<PERSON><PERSON><PERSON>tz Filled Biscuits", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recFnBWJHV", "name": "<PERSON><PERSON><PERSON>tz Filled Biscuits", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec1Zop46R", "name": "<PERSON><PERSON><PERSON> Pretz Filled Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rechrQPJzc", "name": "<PERSON><PERSON><PERSON>tz Filled Biscuits", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recCLn0wER", "name": "Glico Pretz Stuffed Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recDiAlUGm", "name": "QDQ Large - sized Cookie", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recWZxquI0", "name": "BaiXiang - Signature Pork Bone Soup Noodles with Tasty Soup", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recvtpiN4B", "name": "Chips Ahoy! Crispy Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recR4Kzlw9", "name": "Yili Beneficial Digestion Probiotic Yogurt", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recmYTOeCv", "name": "Yi<PERSON>i Yi<PERSON>iao Probiotic Yogurt", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recvA1ngY0", "name": "<PERSON><PERSON><PERSON> Pretz Filled Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec7F9CnUT", "name": "Chips Ahoy! Crispy Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recOS<PERSON>iamh", "name": "White Elephant - Delicious Soup, Golden Soup Beef Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCnfvkurOm", "name": "<PERSON><PERSON><PERSON>tz Filled Biscuits", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recEzrUW8w", "name": "<PERSON><PERSON> Milk Skin Yogurt", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recg7EVMOy", "name": "Lay's <PERSON><PERSON>s", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec9RF7RHj", "name": "Lay's <PERSON><PERSON>s", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recOKPEkVt", "name": "Lay's taro chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recbIUoSO4", "name": "Lay's <PERSON><PERSON>s", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recHy3daSP", "name": "Lay's Thin-Cut Sweet Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recUoyK5Bu", "name": "Glico Pretzels biscuits", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec7NISdrP", "name": "<PERSON><PERSON> Coconut Mania", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec6gvt1C6", "name": "BaiXiang - Street-to-Street Spicy Stir-Fried Pork Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recORz6R9s", "name": "Chips Ahoy! Chocolate Chip Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reca1BGify", "name": "Chips Ahoy! Mini Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recmVRDdOt", "name": "Chips Ahoy! Mini Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec6cotJjs", "name": "Chips Ahoy! Mini Cookies", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec0mbRTqL", "name": "Yili Greek-style Thick Yogurt", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec8AGw3l7", "name": "BaiXiang - Wandering the Streets and Alleys Scallion Oil Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recD9WbuvM", "name": "Lay's <PERSON><PERSON>", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recbqYZ16B", "name": "<PERSON>'s <PERSON><PERSON>", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recJ8uz1P2", "name": "<PERSON>'s <PERSON><PERSON>", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recKM3xdi4", "name": "<PERSON><PERSON>", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recc8yidrh", "name": "<PERSON><PERSON>", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recxrwgt37", "name": "Glico Vegetable Crackers", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recy7Yg5tI", "name": "White Elephant - <PERSON><PERSON> and <PERSON><PERSON>", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reck6IPVQY", "name": "<PERSON>li Fermented Milk with Big Fruit Grains Flavor", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recOsiELtn", "name": "Yili Fermented Milk with Big Fruit Granules Flavor", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recAs6DHwr", "name": "Yili Fermented Milk with Big Fruit Granules Flavor", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recADuhsdj", "name": "BaiXiang - Tasty Old Hen Soup Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recgTajMuQ", "name": "Low-fat and high-protein chicken breast sausage (original flavor)", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recPa8L68B", "name": "A cup of Yili yogurt", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recWtugCZW", "name": "A cup of Yili yogurt", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rechXZLyn9", "name": "A cup of Yili yogurt", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recRtypTfH", "name": "100% NFC Freshly Squeezed Ugly Apple Juice", "platform": "大润发", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recducNSAt", "name": "Purple-skinned cashew nuts", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recYoO1tOg", "name": "Yili Changqing Yogurt Probiotics", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recvfAtGca", "name": "Yili Changqing Yogurt Probiotics", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec9CrHYy8", "name": "Slow-braised Tiger-skin Chicken Feet", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec0i5LJh3", "name": "Yili Classic Yogurt with <PERSON><PERSON><PERSON>", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recbGZ42Y7", "name": "Yili Classic Yogurt with <PERSON><PERSON><PERSON>", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recfSHpFfr", "name": "BaiXiang - Braised Noodles in Pickled Cabbage Soup", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recKypbjrt", "name": "Double Chocolate Crunchy Cereal", "platform": "大润发", "category": "麦片", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recPU3BIWE", "name": "Weilong Konjac Snacks", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recZvNMMfG", "name": "Original Fragrance Black Wheat Soda Crackers", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recKiEWOfG", "name": "<PERSON><PERSON>", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCxoY5qym5", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCxyfvvaNL", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCxyGwF6nP", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCxzf6bjhM", "name": "Lay's potato chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCxzjQozVe", "name": "Lay's potato chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCxzmWEIjd", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCxzpazqXn", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCxzs1n8aP", "name": "Lay's potato chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCxzFLkWro", "name": "Lay's potato chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCxzJpb8K0", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCxzLY3DRp", "name": "Lay's potato chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCxzQB6vut", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCxzTCMpLA", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCxzVI6KOZ", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCxA0DRgbE", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCxCR12MYO", "name": "<PERSON><PERSON>", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec3ukzvYa", "name": "WeiLong Spicy Sticks", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec0RYUPUD", "name": "<PERSON><PERSON>", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recFwjCClJ", "name": "<PERSON><PERSON>", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recqCzx8Cv", "name": "Yili High-Calcium Low-Fat Milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recDKwUOGs", "name": "Weilong Kissing Strips", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recNeJaH3P", "name": "Weilong Kissing Strips", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rect5JHEpo", "name": "Weilong Kissing Strips", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recYx302h5", "name": "<PERSON><PERSON> Roll Gift Box", "platform": "猫超", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recLnEzsXh", "name": "WeiLong Kissing Strips", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recmBAkrY9", "name": "<PERSON><PERSON>-Calcium Milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recVGRTc4G", "name": "<PERSON><PERSON>", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCAQ9JovMZ", "name": "Weilong Konjac Jelly Strips", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCAQUnBV59", "name": "Weilong Konjac Strips", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recaZT7bI9", "name": "BaiXiang - Da<PERSON><PERSON><PERSON>ao Spicy Beef Tallow Hot Pot Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recyTsLMFV", "name": "Yili Sterilized Pure Milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recyEcPFeS", "name": "Yili Pure Milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recnwP7WkX", "name": "<PERSON><PERSON> Skimmed Pure Milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec4kATGHm", "name": "White Elephant - Dajiaojiao Spicy Crayfish Flavor Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCD9tj8S46", "name": "Lay's potato chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec2pdLpBo", "name": "Yili High-Calcium Milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recacshbZT", "name": "White Elephant - Chinese Style Turkey Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec1ZWDDKM", "name": "<PERSON><PERSON> All-round Lactose-free Milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQCEDJLABja", "name": "Yili A2 β-casein Pure Milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "receYFXxVh", "name": "BaiXiang - Delicious Lamb Noodles in Soup", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec7FGLrM7", "name": "QQ Star A2β-casein pure milk + Sanyuan Golden Classic pure milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recgmsOPHd", "name": "<PERSON><PERSON>oal-Burned Probiotic Yogurt", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recA6EUr8Q", "name": "Snack Cat Cat Mixed Vegetable and Fruit Crisps", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recb1MgZip", "name": "<PERSON><PERSON>tein Time Yogurt", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recneAhDLU", "name": "<PERSON><PERSON>tein Time Yogurt", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recORqqLTN", "name": "Yili Old-style Yogurt", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recht6neDc", "name": "Yili Yogurt with Big Fruit Bits", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recHoR28B8", "name": "<PERSON><PERSON>at Grain Milk", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recUzdDwPP", "name": "<PERSON> - <PERSON><PERSON> (Original Flavor)", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recyeD6DiF", "name": "<PERSON>'s Roasted Sunflower Seeds", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recCkZHTq4", "name": "Spiced and Braised Duck Gizzards", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recIUx9Ylv", "name": "White Elephant - Braised Pork Ribs Flavor Noodles", "platform": "猫超", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recL6mDtoP", "name": "Slow-roasted Honey-glazed Chicken Drumettes", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recaIEq71K", "name": "BaiXiang Spicy Beef Flavor Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recFWri11K", "name": "<PERSON><PERSON> Golden Series Milk Powder for the Elderly", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recNoijvuW", "name": "BaiXiang - Big Spicy Jiao Salted Egg Yolk Turkey Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recSaKptM2", "name": "<PERSON><PERSON> Powder for the Elderly", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rechBO9Fa2", "name": "Baixiang - Roaming the Streets Crab Roe Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recX2gKyJN", "name": "<PERSON><PERSON> Goat Milk Powder", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recwRiJkV8", "name": "<PERSON><PERSON> - Relieving Dietary Base Milk Powder", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recP1lTQ83", "name": "Baixiang - Dajiaojiao Creamy Turkey Flavor Noodles for Mixing", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rectnBP19Y", "name": "Yili High Calcium Nutritional Milk Powder for Tall Students", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recKBkwfGi", "name": "Yili Pure Milk Powder", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec1EoSXvB", "name": "<PERSON><PERSON> Lady's Probiotic Fiber Control Milk Powder", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recCYpwDIa", "name": "<PERSON><PERSON>uo Bone Energy Dietary Base Milk Powder", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recxr2Qmoz", "name": "White Elephant - Premium Bone Broth Mushroom Original Pork Bone Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recDVoUVjG", "name": "<PERSON><PERSON> High-Cal<PERSON>um and Low-Fat Milk Powder for the Elderly", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recoGyWWQM", "name": "Yili Milk Tablets", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recsPMdTBl", "name": "Yili Milk Tablets", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recFnjMJBo", "name": "Yili Milk Tablets", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec7InbkVB", "name": "Yili Palace Cheese Variety Frozen Yogurt", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec<PERSON><PERSON><PERSON>d", "name": "Yili Palace Cheese Variety Creamy Cheese", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec2QN9umI", "name": "<PERSON><PERSON> Children's and Students' Growth Formula Milk Powder", "platform": "猫超", "category": "牛奶饮品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recghy1BPG", "name": "BaiXiang - Dajiaojiao Spicy Red Oil Chili Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recL4BZN2G", "name": "BaiXiang - Spicy Garlic-flavored Crayfish Noodles in Red Oil by Dajiaojiao", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recLH8P2yq", "name": "BaiXiang - Tasty Tomato and Beef Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQF0yAMo9ro", "name": "Knorr Low-fat <PERSON>ad Dressing", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQNJTX33r3u", "name": "Knorr Low-fat <PERSON>ad Dressing", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reclyiTSmp", "name": "<PERSON>ma pomelo peel", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recMAhckFT", "name": "Hema Organic Chestnut Kernels", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recEvpzWbR", "name": "<PERSON><PERSON> Dried Tangerine Peel and Plum Strips", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recYkpRFqR", "name": "Tai Tai Le Premium First-Pressed Original-Flavored Light Soy Sauce", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec4cdO9gS", "name": "<PERSON>wei Fresh First Press Light Soy Sauce", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec3rYw36n", "name": "Tai Tai Le Premium Original Flavor Braised Soy Sauce", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recQveIs63", "name": "Tai Tai Le Original Fresh Cooking Fish Soy Sauce", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reco4RGzTJ", "name": "Ta-taste Original Fresh Stir-Fry Fresh Soy Sauce", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recep4Q391", "name": "Tai Tai Le Original Flavor Reduced-Salt Premium Fresh Soy Sauce", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recackALaE", "name": "Tai Tai Le Original Flavor Reduced-Salt Light Soy Sauce", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recLRp0Ddi", "name": "<PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recmfJXCc6", "name": "<PERSON><PERSON> C<PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQTA2BDq8tL", "name": "Hema Garlic-flavored Peanuts", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reckRSSqvX", "name": "<PERSON><PERSON> Large Prunes", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recySaflDx", "name": "<PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recJmKtqHK", "name": "Tai Tai Le Original Fresh Chicken Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQTBZVIOXTc", "name": "Too Good Original Fresh Chicken Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec4Onun9t", "name": "Hema Organic Walnut Kernels", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQTCgDmSFkM", "name": "Tai Tai Original Fresh Chicken Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recY31H1Ww", "name": "Hema Milk-flavored Hawaiian Nuts", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQTCy6ZRDUa", "name": "Tai Tai Le Original Flavor Fresh Chicken Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recYb6EBoq", "name": "<PERSON><PERSON> and Ready-to-Eat Fresh-Locked <PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recwfYIOqG", "name": "Hema Pecan Nuts", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recWgql1xB", "name": "<PERSON> Le Seasoning for Sour Soup with <PERSON>f Slices", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recKbasIgw", "name": "Tai Tai Le Spicy Chicken Seasoning for Big Plate", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recTbqStFZ", "name": "Tai Tai Le Spicy Hot Pot Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recZKzU3fx", "name": "Topnotes Orleans Roasted Chicken Wing Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec5HCpuJZ", "name": "Hema Mustard-flavored Crispy Hawaiian Macadamia Nuts", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recoXXlmaC", "name": "Freshly Selected Original-flavored Sunflower Seeds by <PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec6CGJIVQ", "name": "HEMA Organic Peanuts with Huai Salt", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recY94AHjw", "name": "Hema Freeze-dried Strawberries", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recmTW4UcD", "name": "<PERSON><PERSON>s", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reckvWwSkB", "name": "Hema Dried Tangerine Peel Preserved Waxberries", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rectpLFDPf", "name": "<PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recJpi6YPM", "name": "Hema 100% Coconut Water", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recVoRKZAC", "name": "Hema Mixed Nuts", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdP4r4ZE", "name": "Hema 100% Orange Juice", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reccrQZqDr", "name": "<PERSON><PERSON> and <PERSON><PERSON> Dried Fruits", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recayAIc47", "name": "Apple cider vinegar drink", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recHcJ6fRj", "name": "Hema Purple-skinned Cashew Nuts", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reci6LAnLI", "name": "Hema Low-sugar Sweet Soy Milk", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recjBHeOw4", "name": "<PERSON><PERSON>s Black Plums", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recaDQAK2P", "name": "Hema 100% Apple Juice", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recULSWcd8", "name": "Hema Unsalted Cashew Nuts", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recrDJ0uYK", "name": "<PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recpj0XZfo", "name": "Explosive-juice and plump-fleshed citrus blended fruit juice drink", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recWVV1b1n", "name": "Hema 100% Coconut Water", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recOcYxI0a", "name": "Hema Turkish Dried Apricots", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recwZZ46qA", "name": "<PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recADzKCM7", "name": "<PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec41x15iI", "name": "Tai Tai Le Chicken Essence Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec1OccUeF", "name": "Taitale Chicken Bouillon Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec1omGo1t", "name": "Tai Tai Le Chicken Essence Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recjifsjBH", "name": "Tai Tai Le Chicken essence seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recyfxSerB", "name": "Tastebooster Chicken Essence Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recoOLSXNR", "name": "<PERSON><PERSON> Mixed Mushroom Crisps", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recJ0pfUa9", "name": "<PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recgCcjTFO", "name": "Hema Cloud Mist Guava Bubble Fruit Juice", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec3s7YYjR", "name": "Hema Multicolored Raisins", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recsV8yyoc", "name": "HEMA's Artisan-Brewed Plum Juice", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recF7r9Gma", "name": "<PERSON><PERSON>ted Fruit Crisps", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recIJ3xsue", "name": "Grape and orange mixed fruit juice", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recIDodSSb", "name": "Tai Tai Le Halal Classic Chicken Essence Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recVx211sa", "name": "<PERSON><PERSON> Four-time Scented Jasmine Tea with Fragrant Aroma", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recPZSpCyI", "name": "Tai Tai Le Tian Tian <PERSON> Chicken Essence Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQTQiLnKPDe", "name": "Taitaile <PERSON>ian <PERSON>ian <PERSON> Chicken Essence Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recVeRiX0Q", "name": "Member's Mark <PERSON> 100% Mulberry Juice", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQTQwJbNQUf", "name": "Tai Tai Le Tian Tian <PERSON> Chicken Essence Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recoMGcxvL", "name": "Hema 100% HPP Big Red Watermelon Juice", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec5JLla1W", "name": "Hema Organic Fish Skin Peanuts", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recTj6H5yk", "name": "Hema Honey-coated Shelled Walnuts", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQTS0KY2JvY", "name": "Hema Mixed Nuts", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recWEwYCvR", "name": "Hema NFC Cixi Bayberry Juice", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec896fweK", "name": "Jia <PERSON> 100% NFC Purely Squeezed Kiwi Juice", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reccZFnutM", "name": "Tai Tai Le Abalone Sauce Oyster Sauce", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recw2tEyyq", "name": "Tai Tai Le Reduced-Salt Oyster Sauce", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON> and Job's Tears Drink", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recxBpL7u5", "name": "<PERSON><PERSON> Steamed Fragrant Combo Pack", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recXztLNoy", "name": "Master Kong - <PERSON> Bag Shrimp and Fish Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recNnjVkBn", "name": "Hema Original Flavor Pork Jerky", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recxhdojuT", "name": "Tai Tai Le Fresh Chicken Stock Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recUl8NMpK", "name": "Hema Original Flavor Roasted Peanuts", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recEBxcYrW", "name": "Hema Fresh Coconut Meat Freshly Pressed Thick Coconut Milk", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQTU5GUCyB4", "name": "<PERSON> Fresh Scallop Sauce", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recimErDQU", "name": "Hema Hawaiian nuts", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recvWo7Tg0", "name": "<PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recpeEYhIF", "name": "Master Kong - Classic Braised Beef Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recU8VOjJY", "name": "<PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recWwtmR6u", "name": "Hema Premium Pineapple Chips", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec8aSUKq5", "name": "<PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recU506dMj", "name": "Hema Mixed Vegetable Chips", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recDH6z9A5", "name": "Organic corn juice", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recJi1hDIj", "name": "Prune juice", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recrN8wLgD", "name": "Hema HPP Cold-brewed Baiya Qilan Oolong Tea", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recp2Qo1wj", "name": "Hema Open-mouthed Figs", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recjGDrJ1T", "name": "<PERSON> Le <PERSON>", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recV7WnPw8", "name": "<PERSON> Le Freshness Booster", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recCUhGJIR", "name": "<PERSON><PERSON>-Heart Apple Sparkling Juice", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recb05m2Ve", "name": "Hema Mixed Nuts", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recMiPAzBu", "name": "Hema Organic Northeast Korean Pine Nut Kernels", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec2LnOO6g", "name": "Too Tai Le Super Umami Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recMCNnq0e", "name": "Shanzhamannan Original Hawthorn Juice (Fresh Fruit Flavor) Fruit Juice Beverage", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recansSZRs", "name": "Tai Tai Le Monosodium Glutamate", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec3<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON> Fresh Steamed Autumn Pear Slices", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recoRfO05A", "name": "TooLoo High - stock essence", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdxsMnb0", "name": "A large batch of dried dragon fruit at Hema", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recMa56DQh", "name": "<PERSON><PERSON>ed Pine Nuts", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rechQ2Mc2o", "name": "Tai Tai Le Mushroom Essence Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recl7VTy2S", "name": "Tai Tai Le Vegetable Fresh Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec7v1a0bG", "name": "Hema MAX Japanese Plum Cake", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recizNAV5O", "name": "<PERSON> Tai Le Original Flavor Fresh Seasoning with Added <PERSON><PERSON><PERSON> Flavor", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recV9ylHJ9", "name": "<PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recvguwCMo", "name": "Hema MAX Daily Mixed Berries", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec0OMzusn", "name": "Hema MAX Chinese Date and Kernel Pie", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQU3nGglmot", "name": "Taitale Pure Sesame Oil", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recZUtGNwV", "name": "Hema MAX Prebiotic Hawaiian Macadamia Nuts", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec617GVLK", "name": "Hema MAX Crispy Black Beans", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recheDWtcf", "name": "Tai Tai Le First-Pressed Pure Sesame Oil\n\n说明：“头道纯”意即头道压榨的纯正香油，这里处理为“First-Pressed Pure” ；“小磨”在英文语境中一般不特别强调，重点突出“香油”本质是芝麻油，所以翻译为“Sesame Oil”。", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQU43hbfTme", "name": "Taitale Premium First-Pressed Sesame Oil", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recHoWCl8w", "name": "Master Kong - Classic Shrimp and Fish Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recF9vD5HW", "name": "<PERSON><PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recVlTUOhs", "name": "Hema MAX Lava Dark Chocolate with Hawaiian Nuts", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recFWP7mzW", "name": "Hema MAX Jackfruit Chips", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recl1kOoTx", "name": "Taitale Soup Freshness", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rechwNr190", "name": "Taitale Soup Freshness", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recnEQViJy", "name": "Too Good to Be True Soup Freshness", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recvNgcfsW", "name": "<PERSON> Le Soup Freshness", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recYJ5r5c5", "name": "Master Kong - <PERSON> Bag Braised Beef Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recxX1bFID", "name": "Hema MAX Bursting Plum-Wrapped Grapes", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recVjeXtGs", "name": "<PERSON><PERSON> Boneless Lemon Chicken Feet", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recRl4GNxO", "name": "Hema MAX Prebiotic Coconut Milk and Hawthorn with Liquid Filling", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recLoAeegV", "name": "Hema MAX Seasoned Pistachios", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reclCfBK96", "name": "Hema MAX Crab-flavored Hawaiian Macadamia Nuts", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec29UlO9j", "name": "COFCO Fulinmen <PERSON>", "platform": "天猫旗舰店", "category": "大米", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reclvlp5Mg", "name": "COFCO Fulinmen <PERSON>", "platform": "天猫旗舰店", "category": "大米", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recMfyQwLv", "name": "Hema Chocolate Pudding", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recQpeGxMo", "name": "Hema MAX Freeze-dried Figs", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recINiwj0S", "name": "Master Kong - Classic Braised Beef Noodles (Packed)", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec88AUFLR", "name": "COFCO Fulinmen Long-grained Non-glutinous Rice", "platform": "天猫旗舰店", "category": "大米", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recK8Y2NGd", "name": "COFCO Fulinmen Fine Long Grain Rice", "platform": "天猫旗舰店", "category": "大米", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reccaok290", "name": "Hema MAX Sweet Pea Crisps", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec4XbrBH3", "name": "<PERSON><PERSON>uail <PERSON>s", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recTv7HArN", "name": "Master Kong - Super Big Bag Spicy and Refreshing Golden Soup Beef Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recJ7simdM", "name": "COFCO Fulinmen Taiyu Fragrant Premium Jasmine Rice", "platform": "天猫旗舰店", "category": "大米", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reccc2ulWI", "name": "Master Kong - Classic Tomato and Egg Beef Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recXkkdK7J", "name": "COFCO Fulinmen High-quality Northeast Rice", "platform": "天猫旗舰店", "category": "大米", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recqAoB5rQ", "name": "Hema MAX Juicy <PERSON>k-sliced Yellow Peaches", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reci3a4lUo", "name": "COFCO Fulinmen Golden Selection Northeast Rice", "platform": "天猫旗舰店", "category": "大米", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recSfm3Ozh", "name": "Hema High-protein Air-dried Beef Crisps", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recyfYIfH6", "name": "Master Kong - Classic Mushroom and Chicken Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdmxtojP", "name": "COFCO Fulinmen Five Blessings Northeast Long Grain Fragrant Rice", "platform": "天猫旗舰店", "category": "大米", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recMUxknyk", "name": "<PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQUarmdfXal", "name": "High-quality Northeast rice from COFCO Fulinmen", "platform": "天猫旗舰店", "category": "大米", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recMuf65uH", "name": "Hema MAX Freeze-dried Durian", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec6u5Q8qq", "name": "COFCO Fulinmen Mizhi Millet", "platform": "天猫旗舰店", "category": "大米", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recwdkXHa5", "name": "<PERSON><PERSON> Chocolate Oat Crispy Dumplings", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reck1dqKns", "name": "<PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reczremzfI", "name": "COFCO Fulinmen Northeast Rice with Black Soil Core", "platform": "天猫旗舰店", "category": "大米", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recNrfpjsZ", "name": "<PERSON><PERSON>fu", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recLzxYi12", "name": "COFCO Fulinmen KING FOOD Royal Mary Thai Imported Jasmine Rice", "platform": "天猫旗舰店", "category": "大米", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec5Pw7aYx", "name": "Hema NFC Xinjiang Summer Black Grape Juice", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec1z3ECQW", "name": "COFCO Fulinmen Peaceful Fortune Rice - Northeast Rice", "platform": "天猫旗舰店", "category": "大米", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "receDGbL6W", "name": "Hema NFC Mulberry Juice", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec4uJu99A", "name": "<PERSON><PERSON>y <PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recKiahmRC", "name": "COFCO Wuhu Premium Pearl Rice", "platform": "天猫旗舰店", "category": "大米", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec2VX33k4", "name": "<PERSON><PERSON> Pink Coconut Electrolyte Water", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recY5wWcSH", "name": "COFCO ShanCui Black Fungus", "platform": "天猫旗舰店", "category": "干货烘焙", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recNLw4Qxc", "name": "Hema Pomegranate Cloud and Mist Green Tea", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recgu102nC", "name": "COFCO ShanCui <PERSON>ella Fuciformis", "platform": "天猫旗舰店", "category": "干货烘焙", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recpTZdupG", "name": "COFCO Shancui Log-grown Shiitake Mushrooms", "platform": "天猫旗舰店", "category": "干货烘焙", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQUeGLIGHnN", "name": "COFCO Shan Cui Shi<PERSON>ke Mushrooms", "platform": "天猫旗舰店", "category": "干货烘焙", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recHCvY1iM", "name": "Hema Pure Black Bean Soymilk", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQUf1GGohz3", "name": "COFCO Shancui Antler Mushroom", "platform": "天猫旗舰店", "category": "干货烘焙", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdqcyUTT", "name": "<PERSON><PERSON>'s Ingeniously Boiled Imperata Cylindrica and Sugar Cane Water", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recjAEjzMe", "name": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec9cVcnQv", "name": "Hema Sun-dried Cold-brewed Black Coffee", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recq1XmvcS", "name": "Hema Refreshing Double Chrysanthemum Tea", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recUnZ2Sq2", "name": "<PERSON><PERSON><PERSON><PERSON> Dipping Sauce Hot Pot", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec6BYeqpU", "name": "Hot pot with Haidilao dipping sauce", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec4tEER9m", "name": "Haidilao dipping sauce hot pot", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recMokFhId", "name": "Hot pot with Haidilao dipping sauce", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reciVC9ggb", "name": "<PERSON><PERSON><PERSON><PERSON> Dipping Sauce Hot Pot", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec4zv0XiQ", "name": "Hema Sea Salt Bursting Triple Lemon Drink", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec9EyzSGU", "name": "Haidilao Hot Pot Base", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recTWQxeHH", "name": "Haidilao Hot Pot Base", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec1pgqf1u", "name": "Haidilao Hot Pot Base", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recoPGxUfr", "name": "Haidilao Hot Pot Base", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recyztCULk", "name": "Haidilao Hot Pot Base", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reckgxcKbj", "name": "Haidilao Hot Pot Base", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recpuoAPNl", "name": "Haidilao Hot Pot Base", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recr9xhwIb", "name": "Haidilao Hot Pot Base", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recy33SI7B", "name": "Haidilao Hot Pot Base", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec5fBrfFq", "name": "Haidilao Hot Pot Base", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recIxJS8Rg", "name": "Hema Corn Silk and Brown Rice Tea", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQNIIlf3cvO", "name": "Coca-Cola Joyko Zero-fat Salad Dressing", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQUnFZdIzR9", "name": "Haidilao Hot Pot Base", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQUnMDW7JfB", "name": "Haidilao Hot Pot Base", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQUq0JZwy6R", "name": "Knorr Hot and Sour Soup Mix", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQUq0JZ6IV8", "name": "Knorr Hot and Sour Soup Mix", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQUq0JZuBSh", "name": "Coca-Cola Chicken Mushroom Soup Mix", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQUq0JZLs95", "name": "Coca Kitchen West Lake Beef Broth Soup Mix", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQUq0JZkb1s", "name": "Carnation Cream of Chicken and Corn Soup Mix", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQUq0JZjEVi", "name": "Knorr Sour Soup Beef Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQUq0JZtIyD", "name": "Carréer <PERSON><PERSON><PERSON> and Sour Sauce Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQUq0JZDzDO", "name": "<PERSON><PERSON><PERSON> Braised <PERSON><PERSON> Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQUq0JZcpu2", "name": "Jiale Spicy and Numbing Sauce for Mahjong-style Cold Noodles Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdru0QLa", "name": "Haidilao Crayfish Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recUleHlX0", "name": "Haidilao Crayfish Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recbA3EcoK", "name": "Haidilao Crayfish Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec3fu8lkl", "name": "Master Kong - Classic Spicy Beef Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recUezdGrm", "name": "Master Kong - Classic Coriander Spicy Beef Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recj9Gi28E", "name": "Master Kong - Classic Spicy and Sour Beef Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdeRjXWX", "name": "Master Kong - One and a Half Times the Flavor Braised Beef Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reca6jfHeF", "name": "Master Kong - Classic Black Pepper Steak Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec82LuJdd", "name": "Master Kong - Classic Peppered Pork Belly and Chicken Noodles", "platform": "猫超", "category": "方便速食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQW9hF8ck8i", "name": "Coca Kitchen Thai Coconut Curry Paste Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQW9hF8kXL6", "name": "Jiale Spicy Bullfrog Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQW9hF8fYjv", "name": "Jiale Spicy and Fragrant Hot Pot Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQW9hF8I549", "name": "<PERSON><PERSON>ing", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQW9hF87lan", "name": "Carrefour Gold & Silver Garlic Seafood Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recJxwSmva", "name": "Unif 100% NFC Blueberry Juice", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recZPxUHrL", "name": "Coconut water", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec7PxYDvt", "name": "Unif Oriental Beauty Tea Original Flavor Tea Drink", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recIbhlIKD", "name": "TRADERS DEAL Honey Pomelo Tea", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recyPJxvVd", "name": "Biquan Double Lemon Duck Shit Xiang Lemon Tea", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recNZNyiSO", "name": "Botherless Organic Freeze-dried Strawberries", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recaS6pMsA", "name": "Hawaiian macadamia nuts with scallop and squid flavor", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec5PdD93E", "name": "Botherless Freeze-dried Probiotic Pineberry & Strawberry", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recKNr4Xcl", "name": "Coffee milk", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recCpMUBmK", "name": "Sweet pea chips", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recIN9LVYH", "name": "Starbucks Colombian Coffee Latte, Coffee Beverage", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reccyELUiE", "name": "Thai frozen-dried Monthong durian", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recjJ9FUWV", "name": "Dried Summer Music grapes", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recF2Waphj", "name": "Peeled almond kernels at the budding moment", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdO5zbI2", "name": "Orient Leaf Tangerine Pu'er Compound Tea Beverage", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recCGhOmgx", "name": "Youyou Sour Soup Double-crisp Snack Combo", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recbfnske2", "name": "Raw Pu-erh tea, sugar-free tea beverage", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recHDX6VMJ", "name": "Suntory Black Oolong Tea, Oolong Tea Beverage (Sugar-Free)", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recqCoZhD3", "name": "<PERSON>ma Black Gold Rice Crackers", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recANTmgZf", "name": "SINGHA Lion Soda Water", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recynZdpbt", "name": "Hema Glutinous Rice Crackers", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQYWcVEwhi6", "name": "<PERSON>ma Black Gold Rice Crackers", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recOokWqvy", "name": "Hema <PERSON> and Flaxseed Healthy Water", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec8iG09cv", "name": "Watsons Soda Water", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recxM96dUG", "name": "Hema Apple and Astragalus Root Water", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recDp6vw3w", "name": "HEMA Parsley Baguette Crisps", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recTb6hejs", "name": "<PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec5xGk26p", "name": "He<PERSON> Ficus hirta and <PERSON> Rice Drink", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recH3Yx4AL", "name": "<PERSON><PERSON> Thin Crackers", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recVtbQRK1", "name": "Schweppes Soda Water", "platform": "山姆APP", "category": "酒水饮料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recbTn8xqA", "name": "<PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recblg7Ktj", "name": "Hema Yunnan Glutinous Aromatic Big Sip Tea", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recK9Jsujt", "name": "BALANCE.TT KETO Beef + Processed Cheese", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recbjNBi1A", "name": "Hema Pure Cake", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec9ixpJbZ", "name": "<PERSON><PERSON> and Mist Green Tea", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recsWFx1Wi", "name": "<PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec9t23XDC", "name": "HEMA Orange Soda Pineapple Wave Fizzing Americano", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec0V4cVzG", "name": "<PERSON><PERSON>-<PERSON>tein Shrimp Crackers", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rechN4GhPz", "name": "<PERSON><PERSON>da Crackers", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recfDNnaeV", "name": "Lay's Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recIdMthDY", "name": "Lay's potato chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rech0BzTSo", "name": "Hema Double-leaf and Double-melon Water", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recIcr5AMd", "name": "Hema High Mountain Sliced Colorful Potato Chips", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recTfLL8rn", "name": "Lay's potato chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec6CO6IoP", "name": "Lay's potato chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recSkBm7Hl", "name": "Lay's potato chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdSdW175", "name": "<PERSON><PERSON>", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQZ3Azt1edm", "name": "<PERSON><PERSON>da Crackers", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recGgFtPPq", "name": "Hema 100% HPP Ice Cream Meteor Melon Juice", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recp82dofu", "name": "Lay's Extra Long Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recXXIomeC", "name": "Lay's Extra Long Potato Fries", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recScAMdea", "name": "Lay's Extra Long Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recXs2FE00", "name": "Prebiotic Rock-Baked Seaweed", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQZ4gHeP5aj", "name": "<PERSON><PERSON>-<PERSON>tein Shrimp Crackers", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recCHygMVW", "name": "High-fiber Boletus and Konjac", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reckkFjtZG", "name": "Orion Potato Chip Sticks", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQZ6tpJadY8", "name": "Oishi Country Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recwp932IF", "name": "Egg King Antibiotic-free Quail Eggs", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQZ6HVcCTxF", "name": "Oishi Country Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQZ6I9AAE0T", "name": "Oishi Country Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuQZ7rxSRvyx", "name": "Oishi Country Potato Chips", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recSsQc54e", "name": "<PERSON> Crispy Pork Floss Strips", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recE2fWDzJ", "name": "Member's Mark Air-dried Beef", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recK1Ux5ie", "name": "Orion, la-la-la!", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recmV2OLQv", "name": "Orion, la-la-la!", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recwJWxzG8", "name": "Orion, la-la-la!", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "receOxTgfE", "name": "Orion, la-la-la!", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rechvdpnuP", "name": "<PERSON>you Boneless Duck Feet (Wild Pepper Flavor)", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recDPmmWGv", "name": "<PERSON><PERSON> and Crispy Crackers", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recsDfF1Nj", "name": "Orion, la-la-la!", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recptv<PERSON>a", "name": "Orion, la-la-la!", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recwd9wE0i", "name": "Orion, la-la-la", "platform": "大润发", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec3ln0mX3", "name": "<PERSON><PERSON> and Crispy Crackers", "platform": "盒马APP", "category": "自有品牌", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recdoxq8Ql", "name": "Member's <PERSON>", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recZCKDR5s", "name": "Jingqingyuan Traditional Seaweed with Light-salt Olive Oil", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recEqGH88c", "name": "Braised <PERSON><PERSON> in Gravy (Original Braised Flavor)", "platform": "山姆APP", "category": "休闲零食", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR4D7Ow30Jj", "name": "<PERSON><PERSON>r Freshly Simmered Stock Cubes", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR4DX7CmHn1", "name": "<PERSON><PERSON><PERSON> Freshly Simmered Soup Cubes", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR4N5PUflic", "name": "Licheng Mini Pork Sausages", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR4N5SUB4KL", "name": "Infinite Farm Salt-baked Quail Eggs", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR4Q9o3t5M5", "name": "<PERSON> Lifang Glutinous Rice Twists", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR4Qc92P77C", "name": "Licheng Mini Pork Sausages", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5oKLFDLox", "name": "Skittles", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5oL7Y2JK0", "name": "Yizhi Hamburger Gummy Candies", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuR5oLk8igjX", "name": "Qiyun Mountain Choerospondias Axillaris Cake", "platform": "零食很忙", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recO4gJnMV", "name": "Panpan Menier Dry Cake", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recY9wnhoe", "name": "Panpan Meiniye Dry Cake", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recF6KGHqj", "name": "Panpan Menier Dry Cake", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recbHtAMxp", "name": "Panpan Menier Dry Cake", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reczjc54m0", "name": "<PERSON>", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec1AngRf9", "name": "<PERSON>", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recZGKCuxi", "name": "<PERSON><PERSON> <PERSON>", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recP0i7ogZ", "name": "<PERSON>", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recMcJfb8I", "name": "Daliyuan Eight-Treasure Congee", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec6HjyM8w", "name": "Daliyuan Eight-Treasure Congee", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recRqbWQ0C", "name": "Daliyuan Eight-treasure Congee", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reczT5jxnh", "name": "<PERSON>", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reclRl2iK5", "name": "<PERSON><PERSON><PERSON>", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recigOjYCM", "name": "<PERSON><PERSON> <PERSON>", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reczAxaaPv", "name": "Daliyuan Eight-treasure Congee", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recVi7m2Gj", "name": "Daliyuan Eight-Treasure Congee", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recKBRLJVS", "name": "<PERSON><PERSON> <PERSON>", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recayqUjaY", "name": "<PERSON><PERSON> <PERSON>", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recj7dvOkF", "name": "<PERSON>", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recDzp8Tb0", "name": "Daliyuan Black Whole Wheat Mini Bread", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reccEUCCZP", "name": "Daliyuan Mini Bread", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recQ1CxEwn", "name": "Hsu <PERSON> Chi <PERSON>chima Gift Set", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recTVAvEpP", "name": "Daliyuan French Soft Bread", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recw4KfOZi", "name": "Daliyuan French Soft Bread", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reckX0QbBJ", "name": "<PERSON><PERSON>, Tremella and Konjac Balls", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recS0r9j6k", "name": "Xu Fuji Pineapple Cake", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reczTc6SiW", "name": "Hsu Fu Chi Pineapple Cake", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recgbtSL9g", "name": "Xu Fuji Pineapple Cake", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recUON9ewo", "name": "Hsu Fu Chi Pineapple Cake", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec9T77nPB", "name": "Xu Fuji Pineapple Cake", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recVzh5loA", "name": "Gu Duntun Milk-flavored Taro and Oat Konjac Balls", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec2o2lJrQ", "name": "Gudundun Purple Rice, Tremella and Konjac Balls", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recJlIKitq", "name": "Hsu Fu Chi Pineapple Cake", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recGjRsdMm", "name": "Hsu Fu Chi Pineapple Cake", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec86EOJQd", "name": "Daliyuan Chocolate Flavored Pie Coated Sandwich Cake", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recB8SJPgw", "name": "Hsu Fu Chi Pineapple Cake", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reciXkAT0a", "name": "Daliyuan Chocolate Flavor Pie Cookies", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recXgdTmec", "name": "Hsu Fu Chi Pineapple Cake", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recc7mWZgI", "name": "Daliyuan Cranberry and <PERSON><PERSON> Bread, 800g", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recm1JroZV", "name": "<PERSON><PERSON><PERSON> Black Chocolate-Flavored Stuffed Mini Bread", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec8FguYge", "name": "Hsu Fu Chi Wafer Cookies", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recgvcieiw", "name": "Panpan Meiniye Dry Cake", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recAF1zOpa", "name": "Panpan Menier Dry Cake", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recvwFZiwM", "name": "Panpan Menier Dry Cake", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recLrkc0sf", "name": "Panpan Menier Dry Cake", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recUJL0dis", "name": "Xu Fuji Xiaobaba Pastries", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reckQ5PbcI", "name": "<PERSON> Pastries", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec6m3I8ge", "name": "Xu Fuji Little Baba Pastries", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recB8JTvYi", "name": "Xu Fuji Mini Snack Cakes", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec61zBdMT", "name": "<PERSON><PERSON> <PERSON> Chi Little Bubby <PERSON>ries", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recGNEQ0P9", "name": "Xu Fuji Xiaobaba Pastries", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec5NQyTzw", "name": "Xu Fuji Xiaobaba Pastries", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recNIQz4RR", "name": "Panpan Menier Dry Cake", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reciccAm6q", "name": "Panpan Menier Dry Cake", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec9ROshjG", "name": "Panpan Meiniye Dry Cake", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec1TG5JEf", "name": "Panpan Meiniye Dry Cake", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recnrjmEw2", "name": "Panpan Menier Dry Cake", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuPCuhgK", "name": "Hsu Fu Chi Little Baba Pastries", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reco246c7H", "name": "Hsu Fu Chi Rice Snack Roll", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recWLiqYNf", "name": "Daliyuan's new product: Chocolate Velvet Pie Cake", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recByFyNGr", "name": "Daliyuan's new product, Choco Velvet Pie Cake", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec2kEJMA3", "name": "Xu <PERSON> Rolled Heart Crisps", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recKGXBR58", "name": "Daliyuan Swiss Roll Cake", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reca8QsKcs", "name": "Panpan French Mini Bread", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rectvgLyML", "name": "Daliyuan Swiss Roll Cake Bread", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recluuvDre", "name": "Daliyuan Swiss Roll Cake", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recYRkU24l", "name": "Panpan French Mini Bread", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recT29LytU", "name": "Daliyuan Swiss Roll Cake Bread", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec6UsQrFL", "name": "Xu Fuji Mini Pancakes", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recycr3EBd", "name": "Daliyuan Swiss Roll Cake Bread", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recvXUnJqg", "name": "Daliyuan Swiss Roll Cake Bread", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recE9HjoGl", "name": "Panpan French Mini Bread", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rechlJvDEF", "name": "Hsu Fu Chi Mini Pancakes", "platform": "徐福记天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recR2yevyZ", "name": "Daliyuan Milk Peanuts", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reczQJrqip", "name": "Panpan French Mini Bread", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reclZoNHCL", "name": "Daliyuan Peanut Milk", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recLk0ZTGu", "name": "Daliyuan Peanut Milk", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recaHVTuhG", "name": "Daliyuan Three-yuan Tea", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recYbsZmqQ", "name": "Daliyuan Three-Yuan Tea", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recvddC8y6", "name": "Panpan French Mini Bread", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recDipAvn7", "name": "Daliyuan Three-yuan Tea", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recML5jZ30", "name": "Panpan French Mini Bread", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recbj75piM", "name": "Panpan Menier Multigrain Biscuits", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recXX4hivZ", "name": "Panpan Menier Whole Grain Cookies", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recz98hen9", "name": "Panpan French Mini Bread", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recl56SWUi", "name": "Panpan Meiniye Whole Grain Cookies", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec5xnGFrR", "name": "Panpan Menier Whole Grain Biscuits", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec78N0fZ1", "name": "Panpan Meiniye Dry Cake", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reccefc4Df", "name": "Panpan Menier Multigrain Cookies", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec9YTHLci", "name": "Panpan Menier Coarse Grain Cookies", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recftJlMJj", "name": "Panpan Menier Multigrain Biscuits", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec8006Wtl", "name": "Panpan Menier Whole Grain Cookies", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recySMAy74", "name": "Panpan French Soft Bread", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRbthrQZzxy", "name": "Panpan French Soft Bread", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec2p2mgNE", "name": "Panpan Meniere Whole Grain Biscuits", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRbtYdWsTZ5", "name": "Panpan French Soft Bread", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRbvvJ9O2fl", "name": "Panpan French Soft Bread", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRbw1XoOUW9", "name": "Panpan French Soft Bread", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec3jCYIHS", "name": "Panpan Family Size Potato Chips", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recOZBhQZy", "name": "Panpan Family-style Potato Chips", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recduOggZx", "name": "Panpan Family-sized Potato Chips", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recQvl6kzh", "name": "Panpan Family-sized Potato Chips", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recpgzTjxm", "name": "Panpan Salt-baked Quail Eggs", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec0Ei3u4d", "name": "Panpan Shredded Bread", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRbACplJfyM", "name": "Panpan Shredded Bread", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recUwgBmWw", "name": "Panpan Sea Salt Cheese Crackers", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec2EKLZvR", "name": "Panpan Sea Salt & Cheese Cookies", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRbC9cRv8aB", "name": "Panpan Shredded Bread", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec3yP5eWl", "name": "Panpan Potato Chips in Chicken Nugget with Wheat Aroma Flavor", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRbERtPC9oH", "name": "Panpan Shredded Bread", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recyAnnYrF", "name": "Panpan Daily Fruit and Vegetable Crisps", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reclWk5PIH", "name": "Panpan Old-Style Bread", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRbHz4M0DLs", "name": "Panpan Old-fashioned Bread", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRbHSx6Nlh8", "name": "Panpan Old-fashioned Bread", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recEg23sMr", "name": "Panpan Fruit Flavored Drink", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recb17mFEw", "name": "Panpan Fruit-flavored Beverage", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recfKQe6qN", "name": "Panpan Shredded Bread + Old-Fashioned Bread", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recVt4kzsp", "name": "Panpan Fruit Flavored Beverage", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recocclukd", "name": "Panpan Fruit Flavored Beverage", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recotqNTcN", "name": "Panpan French Mini Bread + Shredded Bread", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec10dG9YY", "name": "Panpan Yogurt Drink \"Yi Qi\"", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recxuDKIh5", "name": "<PERSON><PERSON> \"Together for Good\" <PERSON><PERSON><PERSON><PERSON><PERSON>", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recEl72c08", "name": "Panpan Chocolate Pie", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec49SSVJ4", "name": "Panpan Chocolate Pies", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recPZRHaoZ", "name": "Panpan Soda Crackers", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recz0UF3JL", "name": "Kebike Potato Chips", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec3Zc50LZ", "name": "Kebike Potato Chips", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recNS0HBlo", "name": "Kebike Potato Chips", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recoaiZaPZ", "name": "Kebike Potato Chips", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec81IKcNs", "name": "Panpan Soda Crackers", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec5ds6f6H", "name": "Panpan Soda Crackers", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recnkGFb1H", "name": "Kebike Potato Chips", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recLWTt2AC", "name": "Kebike Potato Chips", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recDtR1EHN", "name": "Panpan Soda Crackers", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recSl7f2sG", "name": "Panpan Pastries Combo with Food and Drinks", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recJ3zVy8H", "name": "Kebike Purely Sliced Potato Chips", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rech08dCBG", "name": "Panpan Soda Crackers", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec3kJgwfU", "name": "Panpan Soda Crackers", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reckyn<PERSON><PERSON><PERSON>", "name": "Panpan Soda Crackers", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recT42PcSf", "name": "Kebike Flower Face Purely Sliced Potato Chips", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recw1dS3lh", "name": "Kebike Floral Pure-cut Potato Chips", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recd4jPtFd", "name": "Panpan Soda Crackers", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recmBU9LX0", "name": "Panpan Pure Cake", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recSNrmJEW", "name": "Kebike Floral Pure-Cut Potato Chips", "platform": "达利园天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRc3GKu9ehk", "name": "Panpan Pure Cake", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec4WiqMmK", "name": "Panpan Soft Waffles", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuF3FMSg", "name": "Panpan Waffle Sticks", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recyFeTT5o", "name": "Panpan Chicken-flavored Crispy Snack (McDonald's Chicken McNuggets-like)", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recM4da3GE", "name": "Panpan Chicken-flavored Crispy Chips with Wheat Aroma", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuQdrs", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuF2cO", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuhuNI", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuEsaG", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuaH3h", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuYHur", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5Mu6ask", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuZKaY", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5MuaFXL", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdo5Muax5m", "name": "未命名产品", "platform": "未知平台", "category": "未分类", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdoJEkN2iS", "name": "Knorr Low-fat Stock Cubes", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdoLpDNT4P", "name": "Knorr Low-fat Concentrated Soup Cubes", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdoLLA7hiw", "name": "Hot pot soup base cube", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdsOwlzLdo", "name": "Hot pot soup base cube", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdsW2FJHyP", "name": "Hot pot soup base cube", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdsXvV27JI", "name": "Hot pot soup base cube", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdsZVUH0pm", "name": "Hot pot soup base cubes", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdBvpCgGEv", "name": "Pickling powder", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdBIYHM2PO", "name": "Knorr marinating powder", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRdBKHyk1Bd", "name": "<PERSON><PERSON><PERSON>", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recsddGU3C", "name": "Panpan Chicken-flavored Wheat Crisps", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recpSgtfc4", "name": "Panpan Chicken-flavored Wheat Chips", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recxAKbYac", "name": "Panpan Chicken Flavored Crispy Potato Chips with Wheat Aroma", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recC7m936u", "name": "Panpan Chicken-flavored Crispy Potato Chips with Wheat Aroma", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec3VCJyoZ", "name": "Panpan Chicken-flavored Potato Chips with Wheat Aroma", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recIP1DREx", "name": "Panpan Chicken-flavored Crispy Potato Chips with Wheat Aroma", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recfHQz4SC", "name": "Panpan Chicken Flavored Crispy Potato Chips with Wheat Aroma", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recOQZ3u9x", "name": "Panpan Chicken Flavored Potato Chips with Wheat Aroma", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recCRfQhrj", "name": "Panpan Black Gold Rice Crackers", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "rec8jn1MTG", "name": "Panpan Black Gold Rice Crackers", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recicVN6DP", "name": "Panpan Black Gold Rice Crackers", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "reccXfHTIi", "name": "Panpan Chicken-flavored Crispy Potato Chips with Wheat Aroma", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recUEilivD", "name": "Panpan Chicken-flavored Wheat Crisps", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recIc24WRC", "name": "Panpan Chicken-flavored Wheat Chips", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recqZ3DzsS", "name": "Panpan Chicken-flavored Crispy Potato Sticks with Wheat Aroma", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRgt3jzFO8d", "name": "Knorr frying powder seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recyvMPGSQ", "name": "Kazike Golden Corn Cob", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRgtCV79HHq", "name": "Knorr frying powder and coating powder", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recuRgu9mCSVOl", "name": "<PERSON><PERSON>'s Fry Flour Seasoning", "platform": "天猫旗舰店", "category": "调料", "collectTime": "2025-07-18T02:39:16.000Z"}, {"productId": "recn7j9p5p", "name": "Panpan Coconut Egg Roll Pastries", "platform": "盼盼天猫旗舰店", "category": "休闲食品", "collectTime": "2025-07-18T02:39:16.000Z"}]}, "recommendations": {"highPriorityFields": ["口味", "生产商"], "totalHighPriorityMissing": 283, "actions": ["在飞书多维表格中补充高优先级字段的缺失信息", "重点关注口味和生产商字段，这些对产品展示很重要", "可以按平台或分类批量处理，提高效率"]}, "platformAnalysis": {"盒马APP": {"total": 134, "missingFlavor": 35, "missingManufacturer": 12}, "胖东来": {"total": 26, "missingFlavor": 6, "missingManufacturer": 0}, "大润发": {"total": 279, "missingFlavor": 22, "missingManufacturer": 46}, "猫超": {"total": 175, "missingFlavor": 1, "missingManufacturer": 44}, "山姆APP": {"total": 80, "missingFlavor": 58, "missingManufacturer": 23}, "天猫旗舰店": {"total": 126, "missingFlavor": 0, "missingManufacturer": 3}, "零食很忙": {"total": 210, "missingFlavor": 9, "missingManufacturer": 0}, "盼盼天猫旗舰店": {"total": 95, "missingFlavor": 2, "missingManufacturer": 0}, "徐福记天猫旗舰店": {"total": 34, "missingFlavor": 0, "missingManufacturer": 0}, "达利园天猫旗舰店": {"total": 40, "missingFlavor": 2, "missingManufacturer": 0}, "未知平台": {"total": 10, "missingFlavor": 10, "missingManufacturer": 10}}}