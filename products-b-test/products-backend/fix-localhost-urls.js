/**
 * 修复数据库中的localhost图片URL
 * 将所有localhost:9000的URL替换为实际的服务器IP地址
 */

require('dotenv').config();
const mongoose = require('mongoose');
const { Product, Image } = require('./dist/models');

// 配置
const OLD_HOST = 'localhost:9000';
const NEW_HOST = '************:9000';

/**
 * 连接数据库
 */
async function connectDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ 数据库连接成功');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    process.exit(1);
  }
}

/**
 * 修复单个URL
 */
function fixUrl(url) {
  if (!url || typeof url !== 'string') return url;
  return url.replace(`http://${OLD_HOST}`, `http://${NEW_HOST}`);
}

/**
 * 修复Product表中的localhost URL
 */
async function fixProductUrls() {
  console.log('\n🔧 修复Product表中的localhost URL...');
  
  try {
    // 查找包含localhost的产品
    const products = await Product.find({
      $or: [
        { 'images.front': { $regex: OLD_HOST } },
        { 'images.back': { $regex: OLD_HOST } },
        { 'images.label': { $regex: OLD_HOST } },
        { 'images.package': { $regex: OLD_HOST } },
        { 'images.gift': { $regex: OLD_HOST } }
      ]
    });

    console.log(`📊 找到 ${products.length} 个包含localhost URL的产品`);

    let updatedCount = 0;
    
    for (const product of products) {
      let hasUpdates = false;
      const updates = {};

      // 检查并修复各种图片类型的URL
      const imageTypes = ['front', 'back', 'label', 'package', 'gift'];
      
      for (const type of imageTypes) {
        const imageData = product.images?.[type];
        
        if (typeof imageData === 'string' && imageData.includes(OLD_HOST)) {
          updates[`images.${type}`] = fixUrl(imageData);
          hasUpdates = true;
          console.log(`  📸 修复 ${product.sequence} 的 ${type} 图片URL`);
        } else if (typeof imageData === 'object' && imageData?.url?.includes(OLD_HOST)) {
          updates[`images.${type}.url`] = fixUrl(imageData.url);
          hasUpdates = true;
          console.log(`  📸 修复 ${product.sequence} 的 ${type} 图片对象URL`);
        }
      }

      // 如果有更新，保存到数据库
      if (hasUpdates) {
        await Product.updateOne({ _id: product._id }, { $set: updates });
        updatedCount++;
      }
    }

    console.log(`✅ Product表修复完成，更新了 ${updatedCount} 个产品`);
    return updatedCount;
    
  } catch (error) {
    console.error('❌ 修复Product表失败:', error);
    throw error;
  }
}

/**
 * 修复Image表中的localhost URL
 */
async function fixImageUrls() {
  console.log('\n🔧 修复Image表中的localhost URL...');
  
  try {
    // 查找包含localhost的图片记录
    const images = await Image.find({
      $or: [
        { publicUrl: { $regex: OLD_HOST } },
        { cdnUrl: { $regex: OLD_HOST } },
        { 'thumbnails.url': { $regex: OLD_HOST } }
      ]
    });

    console.log(`📊 找到 ${images.length} 个包含localhost URL的图片记录`);

    let updatedCount = 0;
    
    for (const image of images) {
      let hasUpdates = false;
      const updates = {};

      // 修复主URL
      if (image.publicUrl && image.publicUrl.includes(OLD_HOST)) {
        updates.publicUrl = fixUrl(image.publicUrl);
        hasUpdates = true;
        console.log(`  📸 修复图片 ${image.imageId} 的publicUrl`);
      }

      // 修复CDN URL
      if (image.cdnUrl && image.cdnUrl.includes(OLD_HOST)) {
        updates.cdnUrl = fixUrl(image.cdnUrl);
        hasUpdates = true;
        console.log(`  📸 修复图片 ${image.imageId} 的cdnUrl`);
      }

      // 修复缩略图URL
      if (image.thumbnails && Array.isArray(image.thumbnails)) {
        const updatedThumbnails = image.thumbnails.map(thumb => {
          if (thumb.url && thumb.url.includes(OLD_HOST)) {
            console.log(`  📸 修复图片 ${image.imageId} 的 ${thumb.size} 缩略图URL`);
            return { ...thumb, url: fixUrl(thumb.url) };
          }
          return thumb;
        });
        
        // 检查是否有缩略图被修复
        const thumbnailsChanged = JSON.stringify(updatedThumbnails) !== JSON.stringify(image.thumbnails);
        if (thumbnailsChanged) {
          updates.thumbnails = updatedThumbnails;
          hasUpdates = true;
        }
      }

      // 如果有更新，保存到数据库
      if (hasUpdates) {
        await Image.updateOne({ _id: image._id }, { $set: updates });
        updatedCount++;
      }
    }

    console.log(`✅ Image表修复完成，更新了 ${updatedCount} 个图片记录`);
    return updatedCount;
    
  } catch (error) {
    console.error('❌ 修复Image表失败:', error);
    throw error;
  }
}

/**
 * 验证修复结果
 */
async function validateFix() {
  console.log('\n🔍 验证修复结果...');
  
  try {
    // 检查Product表中是否还有localhost URL
    const remainingProducts = await Product.countDocuments({
      $or: [
        { 'images.front': { $regex: OLD_HOST } },
        { 'images.back': { $regex: OLD_HOST } },
        { 'images.label': { $regex: OLD_HOST } },
        { 'images.package': { $regex: OLD_HOST } },
        { 'images.gift': { $regex: OLD_HOST } }
      ]
    });

    // 检查Image表中是否还有localhost URL
    const remainingImages = await Image.countDocuments({
      $or: [
        { publicUrl: { $regex: OLD_HOST } },
        { cdnUrl: { $regex: OLD_HOST } },
        { 'thumbnails.url': { $regex: OLD_HOST } }
      ]
    });

    console.log(`📊 验证结果:`);
    console.log(`   Product表剩余localhost URL: ${remainingProducts} 个`);
    console.log(`   Image表剩余localhost URL: ${remainingImages} 个`);

    if (remainingProducts === 0 && remainingImages === 0) {
      console.log('✅ 所有localhost URL已成功修复！');
      return true;
    } else {
      console.log('⚠️  仍有部分localhost URL未修复');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 验证失败:', error);
    return false;
  }
}

/**
 * 主执行函数
 */
async function main() {
  try {
    console.log('🚀 开始修复localhost图片URL...');
    console.log(`📝 替换规则: ${OLD_HOST} -> ${NEW_HOST}`);
    
    // 连接数据库
    await connectDatabase();
    
    // 修复Product表
    const productUpdates = await fixProductUrls();
    
    // 修复Image表
    const imageUpdates = await fixImageUrls();
    
    // 验证修复结果
    const isSuccess = await validateFix();
    
    console.log('\n📊 修复总结:');
    console.log(`   更新的产品数: ${productUpdates}`);
    console.log(`   更新的图片记录数: ${imageUpdates}`);
    console.log(`   修复状态: ${isSuccess ? '✅ 成功' : '❌ 部分失败'}`);
    
    if (isSuccess) {
      console.log('\n🎉 localhost URL修复完成！现在所有图片都使用正确的IP地址。');
    } else {
      console.log('\n⚠️  修复未完全成功，请检查日志并手动处理剩余问题。');
    }
    
  } catch (error) {
    console.error('❌ 修复过程中出现错误:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('📝 数据库连接已关闭');
  }
}

// 执行修复
if (require.main === module) {
  main();
}

module.exports = { fixUrl, fixProductUrls, fixImageUrls, validateFix };
