/**
 * 专门修复Image表中缩略图的localhost URL
 */

require('dotenv').config();
const mongoose = require('mongoose');
const { Image } = require('./dist/models');

// 配置
const OLD_HOST = 'localhost:9000';
const NEW_HOST = '************:9000';

/**
 * 连接数据库
 */
async function connectDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ 数据库连接成功');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    process.exit(1);
  }
}

/**
 * 修复缩略图URL
 */
async function fixThumbnailUrls() {
  console.log('\n🔧 修复Image表中的缩略图localhost URL...');
  
  try {
    // 查找包含localhost缩略图的图片记录
    const images = await Image.find({
      'thumbnails.url': { $regex: OLD_HOST }
    });

    console.log(`📊 找到 ${images.length} 个包含localhost缩略图URL的图片记录`);

    let updatedCount = 0;
    
    for (const image of images) {
      let hasUpdates = false;
      
      // 修复缩略图URL
      if (image.thumbnails && Array.isArray(image.thumbnails)) {
        const updatedThumbnails = image.thumbnails.map(thumb => {
          if (thumb.url && thumb.url.includes(OLD_HOST)) {
            console.log(`  📸 修复图片 ${image.imageId} 的 ${thumb.size} 缩略图URL`);
            return { 
              ...thumb, 
              url: thumb.url.replace(`http://${OLD_HOST}`, `http://${NEW_HOST}`)
            };
          }
          return thumb;
        });
        
        // 直接更新整个thumbnails数组
        await Image.updateOne(
          { _id: image._id }, 
          { $set: { thumbnails: updatedThumbnails } }
        );
        
        updatedCount++;
        hasUpdates = true;
      }
    }

    console.log(`✅ 缩略图URL修复完成，更新了 ${updatedCount} 个图片记录`);
    return updatedCount;
    
  } catch (error) {
    console.error('❌ 修复缩略图URL失败:', error);
    throw error;
  }
}

/**
 * 验证修复结果
 */
async function validateThumbnailFix() {
  console.log('\n🔍 验证缩略图修复结果...');
  
  try {
    // 检查是否还有localhost缩略图URL
    const remainingThumbnails = await Image.countDocuments({
      'thumbnails.url': { $regex: OLD_HOST }
    });

    console.log(`📊 验证结果:`);
    console.log(`   剩余localhost缩略图URL: ${remainingThumbnails} 个`);

    if (remainingThumbnails === 0) {
      console.log('✅ 所有缩略图localhost URL已成功修复！');
      
      // 随机检查几个修复后的记录
      console.log('\n🔍 随机检查修复后的缩略图:');
      const sampleImages = await Image.find({
        'thumbnails.url': { $regex: NEW_HOST }
      }).limit(3);
      
      for (const image of sampleImages) {
        console.log(`图片 ${image.imageId}:`);
        if (image.thumbnails && image.thumbnails.length > 0) {
          image.thumbnails.forEach(thumb => {
            console.log(`  ${thumb.size}: ${thumb.url}`);
          });
        }
      }
      
      return true;
    } else {
      console.log('⚠️  仍有部分缩略图localhost URL未修复');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 验证失败:', error);
    return false;
  }
}

/**
 * 主执行函数
 */
async function main() {
  try {
    console.log('🚀 开始修复缩略图localhost URL...');
    console.log(`📝 替换规则: ${OLD_HOST} -> ${NEW_HOST}`);
    
    // 连接数据库
    await connectDatabase();
    
    // 修复缩略图URL
    const thumbnailUpdates = await fixThumbnailUrls();
    
    // 验证修复结果
    const isSuccess = await validateThumbnailFix();
    
    console.log('\n📊 缩略图修复总结:');
    console.log(`   更新的图片记录数: ${thumbnailUpdates}`);
    console.log(`   修复状态: ${isSuccess ? '✅ 成功' : '❌ 失败'}`);
    
    if (isSuccess) {
      console.log('\n🎉 缩略图localhost URL修复完成！');
    } else {
      console.log('\n⚠️  缩略图修复未完全成功，请检查日志。');
    }
    
  } catch (error) {
    console.error('❌ 修复过程中出现错误:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('📝 数据库连接已关闭');
  }
}

// 执行修复
if (require.main === module) {
  main();
}

module.exports = { fixThumbnailUrls, validateThumbnailFix };
