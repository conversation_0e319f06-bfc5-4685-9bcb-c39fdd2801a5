{"timestamp": "2025-07-21T14:05:52.279Z", "database": "products", "backupPath": "/Users/<USER>/Desktop/projects/products-b-test/products-backend/backups/backup-2025-07-21T14-05-47", "collections": [{"collection": "categories", "documentCount": 0, "filePath": "/Users/<USER>/Desktop/projects/products-b-test/products-backend/backups/backup-2025-07-21T14-05-47/categories.json", "fileSize": 2}, {"collection": "newproducts", "documentCount": 0, "filePath": "/Users/<USER>/Desktop/projects/products-b-test/products-backend/backups/backup-2025-07-21T14-05-47/newproducts.json", "fileSize": 2}, {"collection": "images", "documentCount": 263, "filePath": "/Users/<USER>/Desktop/projects/products-b-test/products-backend/backups/backup-2025-07-21T14-05-47/images.json", "fileSize": 148024}, {"collection": "products", "documentCount": 1818, "filePath": "/Users/<USER>/Desktop/projects/products-b-test/products-backend/backups/backup-2025-07-21T14-05-47/products.json", "fileSize": 6373396}], "databaseStats": {"collectionCount": 4, "documentCount": 2081, "backupSize": 6521424}, "mongodbUri": "mongodb://lcs:****@152.89.168.61:27017/products?authSource=admin", "version": "1.0", "type": "pre-schema-migration", "method": "mongoose-json-export"}