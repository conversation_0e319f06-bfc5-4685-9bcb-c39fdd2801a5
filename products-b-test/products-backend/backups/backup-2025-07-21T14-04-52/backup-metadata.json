{"timestamp": "2025-07-21T14:04:54.541Z", "database": "products", "backupPath": "/Users/<USER>/Desktop/projects/products-b-test/products-backend/backups/backup-2025-07-21T14-04-52", "collections": [{"collection": "categories", "error": "collection.stats is not a function", "status": "failed"}, {"collection": "newproducts", "error": "collection.stats is not a function", "status": "failed"}, {"collection": "images", "error": "collection.stats is not a function", "status": "failed"}, {"collection": "products", "error": "collection.stats is not a function", "status": "failed"}], "databaseStats": {"dataSize": 4882828, "storageSize": 2179072, "indexSize": 2297856, "collectionCount": 4, "documentCount": 2081}, "mongodbUri": "mongodb://lcs:****@152.89.168.61:27017/products?authSource=admin", "version": "1.0", "type": "pre-schema-migration", "method": "mongoose-json-export"}