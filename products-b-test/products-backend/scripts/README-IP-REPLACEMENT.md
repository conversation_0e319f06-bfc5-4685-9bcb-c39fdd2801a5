# 数据库图片链接IP地址批量替换工具

## 概述

这套工具用于安全地将MongoDB数据库中的图片链接从旧IP地址（*************）批量替换为新IP地址（************）。

## 背景

- 图片文件已经物理迁移到************服务器
- 数据库中大部分图片链接仍然指向旧服务器*************
- 需要将数据库中的IP地址从*************批量替换为************

## 工具组成

### 1. 主控制脚本
- **文件**: `ip-replacement-controller.js`
- **功能**: 协调整个替换流程的主控制器

### 2. 备份脚本
- **文件**: `backup-before-ip-replacement.js`
- **功能**: 在执行替换前创建完整的数据库备份

### 3. 批量替换脚本
- **文件**: `batch-ip-replacement.js`
- **功能**: 执行实际的IP地址批量替换操作

### 4. 回滚脚本
- **文件**: `rollback-ip-replacement.js`
- **功能**: 在出现问题时恢复到替换前的状态

### 5. 测试脚本
- **文件**: `test-ip-replacement.js`
- **功能**: 验证所有脚本的功能和环境配置

## 使用方法

### 步骤1: 运行测试（推荐）
```bash
cd products-backend/scripts
node test-ip-replacement.js
```

### 步骤2: 检查当前状态
```bash
node ip-replacement-controller.js status
```

### 步骤3: 执行完整替换流程
```bash
node ip-replacement-controller.js full
```

### 或者分步执行：

#### 3a. 仅执行备份
```bash
node ip-replacement-controller.js backup
```

#### 3b. 仅执行替换
```bash
node ip-replacement-controller.js replace
```

#### 3c. 如需回滚
```bash
node ip-replacement-controller.js rollback
```

## 涉及的数据库字段

### Product集合
- `images.front` - 正面图片URL
- `images.back` - 背面图片URL
- `images.label` - 标签图片URL
- `images.package` - 包装图片URL
- `images.gift` - 礼品图片URL

### Image集合
- `publicUrl` - 公共访问URL
- `cdnUrl` - CDN访问URL
- `thumbnails[].url` - 缩略图URL数组

## 安全特性

### 1. 数据备份
- 执行替换前自动创建完整备份
- 备份包含所有集合的完整数据
- 生成详细的备份元数据

### 2. 操作验证
- 替换前分析需要更新的记录数量
- 替换后验证操作结果
- 详细的操作日志记录

### 3. 回滚机制
- 支持从备份完全恢复数据
- 自动选择最新的相关备份
- 验证回滚操作的完整性

### 4. 错误处理
- 详细的错误日志
- 操作失败时的安全退出
- 数据库连接异常处理

## 输出文件

### 备份文件
- 位置: `../backups/ip-replacement-backup-YYYY-MM-DD-HH-mm-ss/`
- 内容: 所有集合的JSON文件 + 元数据

### 日志文件
- 位置: `../logs/`
- 类型: 
  - `ip-replacement-YYYY-MM-DD-HH-mm-ss.log` - 替换操作日志
  - `ip-rollback-YYYY-MM-DD-HH-mm-ss.log` - 回滚操作日志

### 报告文件
- 位置: `../logs/`
- 类型:
  - `ip-replacement-report-YYYY-MM-DD-HH-mm-ss.json` - 替换操作报告
  - `ip-rollback-report-YYYY-MM-DD-HH-mm-ss.json` - 回滚操作报告

## 环境要求

### 必需的环境变量
```bash
MONGODB_URI=********************************:port/database?authSource=admin
```

### Node.js依赖
- mongoose
- fs (内置)
- path (内置)

## 操作流程

### 完整流程 (`full` 命令)
1. **状态检查**: 分析当前数据库中需要替换的URL数量
2. **数据备份**: 创建完整的数据库备份
3. **执行替换**: 批量替换所有匹配的IP地址
4. **结果验证**: 验证替换操作的完整性和正确性

### 替换规则
- **源IP**: *************
- **目标IP**: ************
- **替换方式**: 全局字符串替换
- **保持不变**: 端口号、路径、文件名等其他部分

## 注意事项

### 执行前检查
1. 确保数据库连接正常
2. 确保有足够的磁盘空间存储备份
3. 建议在非业务高峰期执行
4. 确认新IP地址的服务器正常运行

### 执行中监控
1. 观察控制台输出的进度信息
2. 检查是否有错误或警告信息
3. 确认替换的URL数量符合预期

### 执行后验证
1. 检查验证结果是否通过
2. 随机抽查几个产品的图片链接
3. 确认图片可以正常访问
4. 保留备份文件以备不时之需

## 故障排除

### 常见问题

#### 1. 数据库连接失败
- 检查MONGODB_URI环境变量
- 确认数据库服务器可访问
- 验证用户名和密码

#### 2. 备份失败
- 检查磁盘空间是否充足
- 确认备份目录权限
- 检查数据库用户权限

#### 3. 替换失败
- 查看详细错误日志
- 检查数据库写入权限
- 确认数据格式正确

#### 4. 验证失败
- 检查是否有部分记录更新失败
- 查看操作日志了解具体原因
- 考虑重新执行替换操作

### 紧急回滚
如果替换操作出现严重问题，立即执行：
```bash
node ip-replacement-controller.js rollback
```

## 联系支持

如果遇到问题或需要帮助，请：
1. 查看生成的日志文件
2. 保留备份文件
3. 记录具体的错误信息
4. 联系技术支持团队
