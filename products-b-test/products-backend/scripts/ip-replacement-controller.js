#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { createBackup } = require('./backup-before-ip-replacement');
const { performBatchReplacement } = require('./batch-ip-replacement');
const { performRollback } = require('./rollback-ip-replacement');

console.log('🎯 数据库图片链接IP地址替换控制器');
console.log('=====================================');
console.log('这个脚本将协调整个IP替换流程：');
console.log('1. 数据备份');
console.log('2. IP地址批量替换');
console.log('3. 结果验证');
console.log('=====================================\n');

// 解析命令行参数
const args = process.argv.slice(2);
const command = args[0];

function showUsage() {
  console.log('使用方法:');
  console.log('  node ip-replacement-controller.js backup    # 仅执行备份');
  console.log('  node ip-replacement-controller.js replace   # 仅执行替换（需要先备份）');
  console.log('  node ip-replacement-controller.js rollback  # 执行回滚操作');
  console.log('  node ip-replacement-controller.js full      # 执行完整流程（备份+替换）');
  console.log('  node ip-replacement-controller.js status    # 查看当前状态');
  console.log('');
}

async function checkCurrentStatus() {
  try {
    console.log('🔍 检查当前数据库状态...');
    
    const { analyzeCurrentState, connectToDatabase } = require('./batch-ip-replacement');
    const mongoose = require('mongoose');
    
    await connectToDatabase();
    const analysis = await analyzeCurrentState();
    await mongoose.disconnect();
    
    console.log('\n📊 当前状态报告:');
    console.log('=====================================');
    console.log(`Product集合:`);
    console.log(`  总URL数量: ${analysis.product.total}`);
    console.log(`  需要替换的URL (*************): ${analysis.product.oldIp}`);
    console.log(`  已经是新IP的URL (************): ${analysis.product.newIp}`);
    console.log(`  其他URL: ${analysis.product.other}`);
    
    console.log(`\nImage集合:`);
    console.log(`  总URL数量: ${analysis.image.total}`);
    console.log(`  需要替换的URL (*************): ${analysis.image.oldIp}`);
    console.log(`  已经是新IP的URL (************): ${analysis.image.newIp}`);
    console.log(`  其他URL: ${analysis.image.other}`);
    
    const totalNeedReplacement = analysis.product.oldIp + analysis.image.oldIp;
    console.log(`\n📋 总结:`);
    console.log(`  需要替换的URL总数: ${totalNeedReplacement}`);
    
    if (totalNeedReplacement === 0) {
      console.log('✅ 所有URL都已经是正确的IP地址，无需替换。');
    } else {
      console.log(`⚠️ 发现 ${totalNeedReplacement} 个URL需要从旧IP替换为新IP。`);
    }
    
    return analysis;
    
  } catch (error) {
    console.error('❌ 检查状态失败:', error.message);
    throw error;
  }
}

async function executeBackup() {
  try {
    console.log('🚀 执行数据备份...');
    const result = await createBackup();
    
    if (result.success) {
      console.log('✅ 备份成功完成！');
      console.log(`📁 备份位置: ${result.backupPath}`);
      return result;
    } else {
      throw new Error(result.error || '备份失败');
    }
  } catch (error) {
    console.error('❌ 备份执行失败:', error.message);
    throw error;
  }
}

async function executeReplacement() {
  try {
    console.log('🔄 执行IP地址批量替换...');
    const result = await performBatchReplacement();
    
    if (result.success) {
      console.log('✅ 替换成功完成！');
      console.log(`📊 更新统计: Product(${result.report.summary.productUrlsReplaced}) + Image(${result.report.summary.imageUrlsReplaced}) = ${result.report.summary.totalUrlsReplaced} 个URL`);
      console.log(`📁 详细报告: ${result.reportPath}`);
      return result;
    } else {
      throw new Error(result.error || '替换失败');
    }
  } catch (error) {
    console.error('❌ 替换执行失败:', error.message);
    throw error;
  }
}

async function executeRollback() {
  try {
    console.log('🔄 执行回滚操作...');
    const { selectBackup } = require('./rollback-ip-replacement');
    
    const backupInfo = selectBackup();
    const result = await performRollback(backupInfo);
    
    if (result.success) {
      console.log('✅ 回滚成功完成！');
      console.log(`📊 恢复统计: ${result.report.summary.totalRecordsRestored} 条记录`);
      console.log(`📁 详细报告: ${result.reportPath}`);
      return result;
    } else {
      throw new Error(result.error || '回滚失败');
    }
  } catch (error) {
    console.error('❌ 回滚执行失败:', error.message);
    throw error;
  }
}

async function executeFullProcess() {
  try {
    console.log('🎯 执行完整的IP替换流程...\n');
    
    // 步骤1: 检查当前状态
    console.log('步骤 1/4: 检查当前状态');
    console.log('-------------------');
    const status = await checkCurrentStatus();
    
    const totalNeedReplacement = status.product.oldIp + status.image.oldIp;
    if (totalNeedReplacement === 0) {
      console.log('\n✅ 无需执行替换操作，所有URL都已正确。');
      return { success: true, message: '无需替换' };
    }
    
    // 步骤2: 执行备份
    console.log('\n步骤 2/4: 执行数据备份');
    console.log('-------------------');
    const backupResult = await executeBackup();
    
    // 步骤3: 执行替换
    console.log('\n步骤 3/4: 执行IP地址替换');
    console.log('----------------------');
    const replaceResult = await executeReplacement();
    
    // 步骤4: 最终验证
    console.log('\n步骤 4/4: 最终验证');
    console.log('---------------');
    const finalStatus = await checkCurrentStatus();
    
    const remainingOldIp = finalStatus.product.oldIp + finalStatus.image.oldIp;
    
    console.log('\n🎉 完整流程执行完成！');
    console.log('=====================================');
    console.log(`备份位置: ${backupResult.backupPath}`);
    console.log(`替换报告: ${replaceResult.reportPath}`);
    console.log(`替换的URL数量: ${replaceResult.report.summary.totalUrlsReplaced}`);
    console.log(`剩余需要替换的URL: ${remainingOldIp}`);
    console.log(`最终验证: ${remainingOldIp === 0 ? '✅ 通过' : '❌ 失败'}`);
    
    return {
      success: remainingOldIp === 0,
      backup: backupResult,
      replacement: replaceResult,
      finalStatus: finalStatus
    };
    
  } catch (error) {
    console.error('💥 完整流程执行失败:', error.message);
    console.error('\n🔄 如果需要回滚，请运行:');
    console.error('node ip-replacement-controller.js rollback');
    throw error;
  }
}

async function main() {
  try {
    switch (command) {
      case 'backup':
        await executeBackup();
        break;
        
      case 'replace':
        await executeReplacement();
        break;
        
      case 'rollback':
        await executeRollback();
        break;
        
      case 'full':
        await executeFullProcess();
        break;
        
      case 'status':
        await checkCurrentStatus();
        break;
        
      default:
        console.log('❌ 无效的命令\n');
        showUsage();
        process.exit(1);
    }
    
    console.log('\n✅ 操作成功完成！');
    process.exit(0);
    
  } catch (error) {
    console.error('\n💥 操作失败:', error.message);
    process.exit(1);
  }
}

// 检查命令行参数
if (!command) {
  console.log('❌ 缺少命令参数\n');
  showUsage();
  process.exit(1);
}

// 运行主函数
main();
