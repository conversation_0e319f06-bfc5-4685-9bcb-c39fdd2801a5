#!/usr/bin/env node

const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// 配置
const MONGODB_URI = process.env.MONGODB_URI;
const BACKUP_DIR = path.join(__dirname, '../backups');
const DATE_STAMP = new Date().toISOString().slice(0, 19).replace(/:/g, '-');

console.log('🚀 IP替换前数据库备份脚本');
console.log('=====================================');

async function connectToDatabase() {
  try {
    console.log('🔌 连接到数据库...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ 数据库连接成功');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    throw error;
  }
}

async function exportCollection(collectionName, backupPath) {
  try {
    console.log(`📦 导出集合: ${collectionName}`);
    
    const db = mongoose.connection.db;
    const collection = db.collection(collectionName);
    
    // 获取集合统计信息
    const count = await collection.countDocuments();
    console.log(`   📊 文档数量: ${count}`);
    
    if (count === 0) {
      console.log(`   ⚠️ 集合 ${collectionName} 为空，跳过导出`);
      return {
        collection: collectionName,
        count: 0,
        status: 'empty'
      };
    }
    
    // 导出数据
    const documents = await collection.find({}).toArray();
    const filePath = path.join(backupPath, `${collectionName}.json`);
    
    fs.writeFileSync(filePath, JSON.stringify(documents, null, 2));
    
    console.log(`   ✅ 导出完成: ${filePath}`);
    
    return {
      collection: collectionName,
      count: count,
      filePath: filePath,
      status: 'success'
    };
    
  } catch (error) {
    console.error(`   ❌ 导出集合 ${collectionName} 失败:`, error);
    throw error;
  }
}

async function analyzeImageUrls() {
  try {
    console.log('\n🔍 分析当前图片URL分布...');
    
    const db = mongoose.connection.db;
    
    // 分析Product集合中的图片URL
    const productCollection = db.collection('products');
    const products = await productCollection.find({}).toArray();
    
    let oldIpCount = 0;
    let newIpCount = 0;
    let totalImageUrls = 0;
    
    const oldIp = '*************';
    const newIp = '************';
    
    for (const product of products) {
      if (product.images) {
        for (const [imageType, imageUrl] of Object.entries(product.images)) {
          if (imageUrl && typeof imageUrl === 'string') {
            totalImageUrls++;
            if (imageUrl.includes(oldIp)) {
              oldIpCount++;
            } else if (imageUrl.includes(newIp)) {
              newIpCount++;
            }
          }
        }
      }
    }
    
    // 分析Image集合中的图片URL
    const imageCollection = db.collection('images');
    const images = await imageCollection.find({}).toArray();
    
    let imageCollectionOldIp = 0;
    let imageCollectionNewIp = 0;
    let imageCollectionTotal = 0;
    
    for (const image of images) {
      // 检查publicUrl
      if (image.publicUrl) {
        imageCollectionTotal++;
        if (image.publicUrl.includes(oldIp)) {
          imageCollectionOldIp++;
        } else if (image.publicUrl.includes(newIp)) {
          imageCollectionNewIp++;
        }
      }
      
      // 检查cdnUrl
      if (image.cdnUrl) {
        imageCollectionTotal++;
        if (image.cdnUrl.includes(oldIp)) {
          imageCollectionOldIp++;
        } else if (image.cdnUrl.includes(newIp)) {
          imageCollectionNewIp++;
        }
      }
      
      // 检查thumbnails
      if (image.thumbnails && Array.isArray(image.thumbnails)) {
        for (const thumbnail of image.thumbnails) {
          if (thumbnail.url) {
            imageCollectionTotal++;
            if (thumbnail.url.includes(oldIp)) {
              imageCollectionOldIp++;
            } else if (thumbnail.url.includes(newIp)) {
              imageCollectionNewIp++;
            }
          }
        }
      }
    }
    
    console.log('\n📊 URL分析结果:');
    console.log('=====================================');
    console.log(`Product集合:`);
    console.log(`  总图片URL数量: ${totalImageUrls}`);
    console.log(`  包含旧IP(${oldIp})的URL: ${oldIpCount}`);
    console.log(`  包含新IP(${newIp})的URL: ${newIpCount}`);
    console.log(`  其他URL: ${totalImageUrls - oldIpCount - newIpCount}`);
    
    console.log(`\nImage集合:`);
    console.log(`  总图片URL数量: ${imageCollectionTotal}`);
    console.log(`  包含旧IP(${oldIp})的URL: ${imageCollectionOldIp}`);
    console.log(`  包含新IP(${newIp})的URL: ${imageCollectionNewIp}`);
    console.log(`  其他URL: ${imageCollectionTotal - imageCollectionOldIp - imageCollectionNewIp}`);
    
    return {
      product: {
        total: totalImageUrls,
        oldIp: oldIpCount,
        newIp: newIpCount,
        other: totalImageUrls - oldIpCount - newIpCount
      },
      image: {
        total: imageCollectionTotal,
        oldIp: imageCollectionOldIp,
        newIp: imageCollectionNewIp,
        other: imageCollectionTotal - imageCollectionOldIp - imageCollectionNewIp
      }
    };
    
  } catch (error) {
    console.error('❌ 分析图片URL失败:', error);
    throw error;
  }
}

async function createBackup() {
  try {
    console.log('🚀 开始IP替换前数据库备份...');
    
    // 确保备份目录存在
    if (!fs.existsSync(BACKUP_DIR)) {
      fs.mkdirSync(BACKUP_DIR, { recursive: true });
      console.log(`📁 创建备份目录: ${BACKUP_DIR}`);
    }
    
    const backupPath = path.join(BACKUP_DIR, `ip-replacement-backup-${DATE_STAMP}`);
    fs.mkdirSync(backupPath, { recursive: true });
    
    console.log(`💾 备份位置: ${backupPath}`);
    
    // 连接到数据库
    await connectToDatabase();
    
    // 分析当前URL分布
    const urlAnalysis = await analyzeImageUrls();
    
    // 获取所有集合名称
    const db = mongoose.connection.db;
    const collections = await db.listCollections().toArray();
    const collectionNames = collections.map(c => c.name);
    
    console.log(`\n📋 发现集合: ${collectionNames.join(', ')}`);
    
    // 导出每个集合
    const backupResults = [];
    for (const collectionName of collectionNames) {
      try {
        const result = await exportCollection(collectionName, backupPath);
        backupResults.push(result);
      } catch (error) {
        console.warn(`⚠️ 跳过集合 ${collectionName}:`, error.message);
        backupResults.push({
          collection: collectionName,
          error: error.message,
          status: 'failed'
        });
      }
    }
    
    // 创建备份元数据
    const metadata = {
      timestamp: new Date().toISOString(),
      purpose: 'IP地址替换前备份',
      database: mongoose.connection.name,
      backupPath: backupPath,
      mongodbUri: MONGODB_URI.replace(/:[^:]*@/, ':****@'), // 隐藏密码
      version: '1.0',
      type: 'pre-ip-replacement',
      urlAnalysis: urlAnalysis,
      collections: backupResults,
      replacementPlan: {
        oldIp: '*************',
        newIp: '************',
        affectedCollections: ['products', 'images'],
        estimatedChanges: urlAnalysis.product.oldIp + urlAnalysis.image.oldIp
      }
    };
    
    // 保存元数据
    const metadataPath = path.join(backupPath, 'backup-metadata.json');
    fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));
    
    // 统计结果
    const successfulCollections = backupResults.filter(r => r.status === 'success');
    const failedCollections = backupResults.filter(r => r.status === 'failed');
    const emptyCollections = backupResults.filter(r => r.status === 'empty');
    
    console.log('\n✅ 备份完成！');
    console.log('=====================================');
    console.log(`📊 备份统计:`);
    console.log(`  成功备份: ${successfulCollections.length} 个集合`);
    console.log(`  空集合: ${emptyCollections.length} 个集合`);
    console.log(`  失败: ${failedCollections.length} 个集合`);
    console.log(`  备份位置: ${backupPath}`);
    console.log(`  元数据文件: ${metadataPath}`);
    
    if (failedCollections.length > 0) {
      console.log('\n⚠️ 失败的集合:');
      failedCollections.forEach(f => {
        console.log(`  - ${f.collection}: ${f.error}`);
      });
    }
    
    console.log('\n🔍 预计替换影响:');
    console.log(`  Product集合需要更新的URL: ${urlAnalysis.product.oldIp}`);
    console.log(`  Image集合需要更新的URL: ${urlAnalysis.image.oldIp}`);
    console.log(`  总计需要替换的URL: ${urlAnalysis.product.oldIp + urlAnalysis.image.oldIp}`);
    
    return {
      success: successfulCollections.length > 0,
      backupPath: backupPath,
      metadata: metadata,
      results: backupResults
    };
    
  } catch (error) {
    console.error('💥 备份过程出错:', error);
    return {
      success: false,
      error: error.message
    };
  } finally {
    // 关闭数据库连接
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 运行备份
if (require.main === module) {
  createBackup()
    .then(result => {
      if (result.success) {
        console.log('\n🎉 备份成功完成！');
        console.log('现在可以安全地执行IP替换操作。');
        process.exit(0);
      } else {
        console.error('\n❌ 备份失败！');
        console.error('请解决问题后重新运行备份。');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 备份脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { createBackup };
