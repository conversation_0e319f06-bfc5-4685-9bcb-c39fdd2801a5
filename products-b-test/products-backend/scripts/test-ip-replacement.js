#!/usr/bin/env node

const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

console.log('🧪 IP替换脚本测试套件');
console.log('=====================================');

// 测试配置
const MONGODB_URI = process.env.MONGODB_URI;
const TEST_LOG_DIR = path.join(__dirname, '../test-logs');

// 确保测试日志目录存在
if (!fs.existsSync(TEST_LOG_DIR)) {
  fs.mkdirSync(TEST_LOG_DIR, { recursive: true });
}

const testLogFile = path.join(TEST_LOG_DIR, `ip-replacement-test-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.log`);

function testLog(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}`;
  console.log(message);
  fs.appendFileSync(testLogFile, logMessage + '\n');
}

async function connectToDatabase() {
  try {
    testLog('🔌 连接到测试数据库...');
    await mongoose.connect(MONGODB_URI);
    testLog('✅ 数据库连接成功');
  } catch (error) {
    testLog(`❌ 数据库连接失败: ${error.message}`);
    throw error;
  }
}

async function testDatabaseConnection() {
  try {
    testLog('\n🧪 测试 1: 数据库连接');
    testLog('========================');
    
    await connectToDatabase();
    
    // 测试基本查询
    const db = mongoose.connection.db;
    const collections = await db.listCollections().toArray();
    testLog(`📊 发现 ${collections.length} 个集合`);
    
    // 检查关键集合是否存在
    const requiredCollections = ['products', 'images'];
    const existingCollections = collections.map(c => c.name);
    
    for (const collectionName of requiredCollections) {
      if (existingCollections.includes(collectionName)) {
        const collection = db.collection(collectionName);
        const count = await collection.countDocuments();
        testLog(`✅ 集合 ${collectionName}: ${count} 条记录`);
      } else {
        testLog(`⚠️ 集合 ${collectionName}: 不存在`);
      }
    }
    
    await mongoose.disconnect();
    testLog('✅ 数据库连接测试通过');
    return true;
    
  } catch (error) {
    testLog(`❌ 数据库连接测试失败: ${error.message}`);
    return false;
  }
}

async function testAnalysisFunction() {
  try {
    testLog('\n🧪 测试 2: 数据分析功能');
    testLog('========================');
    
    const { connectToDatabase, analyzeCurrentState } = require('./batch-ip-replacement');
    
    await connectToDatabase();
    const analysis = await analyzeCurrentState();
    await mongoose.disconnect();
    
    // 验证分析结果结构
    const requiredFields = ['product', 'image'];
    const requiredSubFields = ['total', 'oldIp', 'newIp', 'other', 'details'];
    
    for (const field of requiredFields) {
      if (!analysis[field]) {
        throw new Error(`分析结果缺少字段: ${field}`);
      }
      
      for (const subField of requiredSubFields) {
        if (analysis[field][subField] === undefined) {
          throw new Error(`分析结果缺少子字段: ${field}.${subField}`);
        }
      }
    }
    
    testLog(`📊 Product集合分析:`);
    testLog(`   总URL: ${analysis.product.total}`);
    testLog(`   旧IP: ${analysis.product.oldIp}`);
    testLog(`   新IP: ${analysis.product.newIp}`);
    testLog(`   其他: ${analysis.product.other}`);
    
    testLog(`📊 Image集合分析:`);
    testLog(`   总URL: ${analysis.image.total}`);
    testLog(`   旧IP: ${analysis.image.oldIp}`);
    testLog(`   新IP: ${analysis.image.newIp}`);
    testLog(`   其他: ${analysis.image.other}`);
    
    testLog('✅ 数据分析功能测试通过');
    return { success: true, analysis };
    
  } catch (error) {
    testLog(`❌ 数据分析功能测试失败: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function testBackupFunction() {
  try {
    testLog('\n🧪 测试 3: 备份功能');
    testLog('==================');
    
    const { createBackup } = require('./backup-before-ip-replacement');
    
    testLog('🚀 执行测试备份...');
    const result = await createBackup();
    
    if (!result.success) {
      throw new Error(result.error || '备份失败');
    }
    
    // 验证备份文件
    const backupPath = result.backupPath;
    const metadataPath = path.join(backupPath, 'backup-metadata.json');
    
    if (!fs.existsSync(backupPath)) {
      throw new Error(`备份目录不存在: ${backupPath}`);
    }
    
    if (!fs.existsSync(metadataPath)) {
      throw new Error(`备份元数据文件不存在: ${metadataPath}`);
    }
    
    const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
    
    // 验证元数据结构
    const requiredMetadataFields = ['timestamp', 'purpose', 'database', 'backupPath', 'type', 'urlAnalysis'];
    for (const field of requiredMetadataFields) {
      if (!metadata[field]) {
        throw new Error(`备份元数据缺少字段: ${field}`);
      }
    }
    
    testLog(`📁 备份位置: ${backupPath}`);
    testLog(`📅 备份时间: ${metadata.timestamp}`);
    testLog(`📊 备份类型: ${metadata.type}`);
    testLog(`🔍 预计影响: ${metadata.replacementPlan?.estimatedChanges || 'N/A'} 个URL`);
    
    testLog('✅ 备份功能测试通过');
    return { success: true, backupPath, metadata };
    
  } catch (error) {
    testLog(`❌ 备份功能测试失败: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function testScriptIntegration() {
  try {
    testLog('\n🧪 测试 4: 脚本集成');
    testLog('==================');
    
    // 测试脚本文件是否存在
    const scriptFiles = [
      'backup-before-ip-replacement.js',
      'batch-ip-replacement.js',
      'rollback-ip-replacement.js',
      'ip-replacement-controller.js'
    ];
    
    const scriptsDir = __dirname;
    
    for (const scriptFile of scriptFiles) {
      const scriptPath = path.join(scriptsDir, scriptFile);
      if (fs.existsSync(scriptPath)) {
        testLog(`✅ 脚本文件存在: ${scriptFile}`);
        
        // 尝试加载模块
        try {
          require(scriptPath);
          testLog(`✅ 脚本模块加载成功: ${scriptFile}`);
        } catch (error) {
          testLog(`⚠️ 脚本模块加载警告 ${scriptFile}: ${error.message}`);
        }
      } else {
        testLog(`❌ 脚本文件不存在: ${scriptFile}`);
        throw new Error(`缺少脚本文件: ${scriptFile}`);
      }
    }
    
    testLog('✅ 脚本集成测试通过');
    return { success: true };
    
  } catch (error) {
    testLog(`❌ 脚本集成测试失败: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function testDirectoryStructure() {
  try {
    testLog('\n🧪 测试 5: 目录结构');
    testLog('==================');
    
    const requiredDirs = [
      '../backups',
      '../logs',
      '../test-logs'
    ];
    
    for (const dir of requiredDirs) {
      const dirPath = path.join(__dirname, dir);
      if (fs.existsSync(dirPath)) {
        testLog(`✅ 目录存在: ${dir}`);
      } else {
        testLog(`📁 创建目录: ${dir}`);
        fs.mkdirSync(dirPath, { recursive: true });
        testLog(`✅ 目录创建成功: ${dir}`);
      }
    }
    
    testLog('✅ 目录结构测试通过');
    return { success: true };
    
  } catch (error) {
    testLog(`❌ 目录结构测试失败: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function runAllTests() {
  const startTime = Date.now();
  
  try {
    testLog('🚀 开始运行IP替换脚本测试套件...');
    testLog(`📝 测试日志: ${testLogFile}`);
    
    const testResults = [];
    
    // 运行所有测试
    const tests = [
      { name: '数据库连接', func: testDatabaseConnection },
      { name: '目录结构', func: testDirectoryStructure },
      { name: '脚本集成', func: testScriptIntegration },
      { name: '数据分析功能', func: testAnalysisFunction },
      { name: '备份功能', func: testBackupFunction }
    ];
    
    for (const test of tests) {
      try {
        const result = await test.func();
        testResults.push({
          name: test.name,
          success: result === true || result.success === true,
          result: result,
          error: result.error || null
        });
      } catch (error) {
        testResults.push({
          name: test.name,
          success: false,
          result: null,
          error: error.message
        });
      }
    }
    
    // 生成测试报告
    const totalTime = Date.now() - startTime;
    const passedTests = testResults.filter(r => r.success);
    const failedTests = testResults.filter(r => !r.success);
    
    testLog('\n📊 测试结果汇总');
    testLog('=====================================');
    testLog(`总测试数: ${testResults.length}`);
    testLog(`通过: ${passedTests.length}`);
    testLog(`失败: ${failedTests.length}`);
    testLog(`总耗时: ${(totalTime / 1000).toFixed(2)} 秒`);
    
    if (failedTests.length > 0) {
      testLog('\n❌ 失败的测试:');
      failedTests.forEach(test => {
        testLog(`  - ${test.name}: ${test.error}`);
      });
    }
    
    // 保存测试报告
    const testReport = {
      timestamp: new Date().toISOString(),
      duration: `${(totalTime / 1000).toFixed(2)} 秒`,
      summary: {
        total: testResults.length,
        passed: passedTests.length,
        failed: failedTests.length,
        success: failedTests.length === 0
      },
      tests: testResults,
      environment: {
        nodeVersion: process.version,
        mongodbUri: MONGODB_URI.replace(/:[^:]*@/, ':****@')
      }
    };
    
    const reportPath = path.join(TEST_LOG_DIR, `test-report-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(testReport, null, 2));
    
    testLog(`\n📄 详细报告: ${reportPath}`);
    
    if (failedTests.length === 0) {
      testLog('\n🎉 所有测试通过！IP替换脚本已准备就绪。');
      return { success: true, report: testReport };
    } else {
      testLog('\n⚠️ 部分测试失败，请检查并修复问题后重新测试。');
      return { success: false, report: testReport };
    }
    
  } catch (error) {
    testLog(`💥 测试套件执行失败: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// 运行测试
if (require.main === module) {
  runAllTests()
    .then(result => {
      if (result.success) {
        console.log('\n✅ 测试套件执行成功！');
        process.exit(0);
      } else {
        console.error('\n❌ 测试套件执行失败！');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 测试套件崩溃:', error);
      process.exit(1);
    });
}

module.exports = { runAllTests };
