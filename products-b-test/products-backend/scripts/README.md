# IP地址批量替换工具使用说明

## 概述

本工具集用于安全地将数据库中的图片链接IP地址从 `*************` 批量替换为 `************`。

## 工具组成

### 1. 主控制脚本
- **文件**: `master-ip-replacement.js`
- **功能**: 提供交互式菜单，整合所有操作
- **推荐**: 首次使用建议通过此脚本执行

### 2. 核心脚本

#### 备份脚本
- **文件**: `backup-before-ip-replacement.js`
- **功能**: 创建完整的数据库备份
- **输出**: 
  - 备份文件: `../backups/ip-replacement-backup-{timestamp}/`
  - 备份报告: `backup-report.md`

#### 预检查脚本
- **文件**: `ip-replacement-precheck.js`
- **功能**: 分析将要更新的记录数量和详情
- **输出**: 
  - 预检查报告: `../reports/ip-replacement-precheck-{timestamp}.json`

#### 替换脚本
- **文件**: `safe-ip-replacement.js`
- **功能**: 执行安全的批量IP地址替换
- **特性**: 
  - 批处理（100条/批）
  - 详细日志记录
  - 错误处理
- **输出**: 
  - 操作日志: `../logs/ip-replacement-{timestamp}.log`
  - 操作报告: `../logs/ip-replacement-report-{timestamp}.json`

#### 验证脚本
- **文件**: `verify-ip-replacement.js`
- **功能**: 验证替换操作的正确性和完整性
- **检查项**: 
  - 源IP是否完全替换
  - 目标IP数量统计
  - URL格式验证
  - 抽样可访问性检查
- **输出**: 
  - 验证报告: `../reports/ip-replacement-verification-{timestamp}.json`

#### 回滚脚本
- **文件**: `rollback-ip-replacement.js`
- **功能**: 从备份恢复数据库到替换前状态
- **输出**: 
  - 回滚报告: `../reports/ip-replacement-rollback-{timestamp}.json`

## 使用方法

### 方法一：使用主控制脚本（推荐）

```bash
cd products-backend/scripts
node master-ip-replacement.js
```

选择 "1. 执行完整替换流程" 即可自动完成：
1. 数据库备份
2. 预检查分析
3. IP地址替换
4. 结果验证

### 方法二：手动执行各个步骤

```bash
# 1. 创建备份
node backup-before-ip-replacement.js

# 2. 预检查分析
node ip-replacement-precheck.js

# 3. 执行替换
node safe-ip-replacement.js

# 4. 验证结果
node verify-ip-replacement.js

# 5. 如需回滚
node rollback-ip-replacement.js
```

## 安全特性

### 数据安全
- ✅ 操作前自动创建完整备份
- ✅ 支持一键回滚到备份状态
- ✅ 批处理避免内存溢出
- ✅ 详细的错误日志记录

### 操作验证
- ✅ 预检查显示将要更新的记录数量
- ✅ 替换后自动验证结果正确性
- ✅ URL格式和可访问性检查
- ✅ 完整的操作审计日志

## 涉及的数据库字段

### Product集合
- `images.front` - 产品正面图片
- `images.back` - 产品背面图片
- `images.label` - 产品标签图片
- `images.package` - 产品包装图片
- `images.gift` - 产品礼品图片

### Image集合
- `publicUrl` - 公开访问URL
- `cdnUrl` - CDN访问URL
- `thumbnails[].url` - 缩略图URL（small, medium, large）

## 输出目录结构

```
products-backend/
├── scripts/           # 脚本文件
├── backups/          # 备份文件
│   └── ip-replacement-backup-{timestamp}/
│       ├── products.json
│       ├── images.json
│       ├── backup-metadata.json
│       └── backup-report.md
├── logs/             # 操作日志
│   ├── ip-replacement-{timestamp}.log
│   ├── ip-replacement-errors-{timestamp}.log
│   └── ip-replacement-report-{timestamp}.json
└── reports/          # 分析报告
    ├── ip-replacement-precheck-{timestamp}.json
    ├── ip-replacement-verification-{timestamp}.json
    └── ip-replacement-rollback-{timestamp}.json
```

## 注意事项

### 执行前准备
1. 确保数据库连接正常
2. 确保有足够的磁盘空间存储备份
3. 建议在非生产环境先测试
4. 通知相关人员维护时间

### 执行过程中
1. 不要中断正在运行的脚本
2. 关注控制台输出的进度信息
3. 如发现异常立即停止操作

### 执行后验证
1. 检查验证脚本的输出结果
2. 测试应用程序图片显示功能
3. 确认新IP地址的服务器正常运行
4. 保留备份文件直到确认无问题

## 故障排除

### 常见问题
1. **数据库连接失败**: 检查 `.env` 文件中的 `MONGODB_URI` 配置
2. **备份文件过大**: 检查磁盘空间，考虑清理旧备份
3. **替换不完整**: 查看错误日志，可能需要手动处理特殊情况
4. **验证失败**: 检查网络连接和目标服务器状态

### 紧急回滚
如果替换后发现严重问题：
```bash
node rollback-ip-replacement.js
```
此操作将完全恢复数据库到备份状态。

## 技术支持

如遇到问题，请提供以下信息：
1. 错误日志文件内容
2. 操作报告文件内容
3. 数据库和应用程序版本信息
4. 具体的错误现象描述

---

**重要提醒**: 此工具涉及数据库批量修改操作，请务必在充分测试后再在生产环境使用。
