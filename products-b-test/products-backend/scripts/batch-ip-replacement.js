#!/usr/bin/env node

const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// 配置
const MONGODB_URI = process.env.MONGODB_URI;
const OLD_IP = '*************';
const NEW_IP = '************';
const LOG_DIR = path.join(__dirname, '../logs');
const DATE_STAMP = new Date().toISOString().slice(0, 19).replace(/:/g, '-');

console.log('🔄 数据库图片链接IP地址批量替换脚本');
console.log('=====================================');
console.log(`旧IP地址: ${OLD_IP}`);
console.log(`新IP地址: ${NEW_IP}`);
console.log('=====================================');

// 确保日志目录存在
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

const logFile = path.join(LOG_DIR, `ip-replacement-${DATE_STAMP}.log`);

function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}`;
  console.log(message);
  fs.appendFileSync(logFile, logMessage + '\n');
}

async function connectToDatabase() {
  try {
    log('🔌 连接到数据库...');
    await mongoose.connect(MONGODB_URI);
    log('✅ 数据库连接成功');
  } catch (error) {
    log(`❌ 数据库连接失败: ${error.message}`);
    throw error;
  }
}

async function analyzeCurrentState() {
  try {
    log('\n🔍 分析当前数据状态...');
    
    const db = mongoose.connection.db;
    const analysis = {
      product: { total: 0, oldIp: 0, newIp: 0, other: 0, details: [] },
      image: { total: 0, oldIp: 0, newIp: 0, other: 0, details: [] }
    };
    
    // 分析Product集合
    log('📊 分析Product集合...');
    const productCollection = db.collection('products');
    const products = await productCollection.find({}).toArray();
    
    for (const product of products) {
      if (product.images) {
        for (const [imageType, imageUrl] of Object.entries(product.images)) {
          if (imageUrl && typeof imageUrl === 'string') {
            analysis.product.total++;
            if (imageUrl.includes(OLD_IP)) {
              analysis.product.oldIp++;
              analysis.product.details.push({
                productId: product.productId,
                imageType: imageType,
                currentUrl: imageUrl,
                newUrl: imageUrl.replace(new RegExp(OLD_IP, 'g'), NEW_IP)
              });
            } else if (imageUrl.includes(NEW_IP)) {
              analysis.product.newIp++;
            } else {
              analysis.product.other++;
            }
          }
        }
      }
    }
    
    // 分析Image集合
    log('📊 分析Image集合...');
    const imageCollection = db.collection('images');
    const images = await imageCollection.find({}).toArray();
    
    for (const image of images) {
      // 检查publicUrl
      if (image.publicUrl) {
        analysis.image.total++;
        if (image.publicUrl.includes(OLD_IP)) {
          analysis.image.oldIp++;
          analysis.image.details.push({
            imageId: image.imageId,
            field: 'publicUrl',
            currentUrl: image.publicUrl,
            newUrl: image.publicUrl.replace(new RegExp(OLD_IP, 'g'), NEW_IP)
          });
        } else if (image.publicUrl.includes(NEW_IP)) {
          analysis.image.newIp++;
        } else {
          analysis.image.other++;
        }
      }
      
      // 检查cdnUrl
      if (image.cdnUrl) {
        analysis.image.total++;
        if (image.cdnUrl.includes(OLD_IP)) {
          analysis.image.oldIp++;
          analysis.image.details.push({
            imageId: image.imageId,
            field: 'cdnUrl',
            currentUrl: image.cdnUrl,
            newUrl: image.cdnUrl.replace(new RegExp(OLD_IP, 'g'), NEW_IP)
          });
        } else if (image.cdnUrl.includes(NEW_IP)) {
          analysis.image.newIp++;
        } else {
          analysis.image.other++;
        }
      }
      
      // 检查thumbnails
      if (image.thumbnails && Array.isArray(image.thumbnails)) {
        for (let i = 0; i < image.thumbnails.length; i++) {
          const thumbnail = image.thumbnails[i];
          if (thumbnail.url) {
            analysis.image.total++;
            if (thumbnail.url.includes(OLD_IP)) {
              analysis.image.oldIp++;
              analysis.image.details.push({
                imageId: image.imageId,
                field: `thumbnails[${i}].url`,
                currentUrl: thumbnail.url,
                newUrl: thumbnail.url.replace(new RegExp(OLD_IP, 'g'), NEW_IP)
              });
            } else if (thumbnail.url.includes(NEW_IP)) {
              analysis.image.newIp++;
            } else {
              analysis.image.other++;
            }
          }
        }
      }
    }
    
    log('\n📊 分析结果:');
    log('=====================================');
    log(`Product集合:`);
    log(`  总URL数量: ${analysis.product.total}`);
    log(`  需要替换的URL: ${analysis.product.oldIp}`);
    log(`  已经是新IP的URL: ${analysis.product.newIp}`);
    log(`  其他URL: ${analysis.product.other}`);
    
    log(`\nImage集合:`);
    log(`  总URL数量: ${analysis.image.total}`);
    log(`  需要替换的URL: ${analysis.image.oldIp}`);
    log(`  已经是新IP的URL: ${analysis.image.newIp}`);
    log(`  其他URL: ${analysis.image.other}`);
    
    log(`\n总计需要替换的URL: ${analysis.product.oldIp + analysis.image.oldIp}`);
    
    return analysis;
    
  } catch (error) {
    log(`❌ 分析数据状态失败: ${error.message}`);
    throw error;
  }
}

async function confirmReplacement(analysis) {
  const totalReplacements = analysis.product.oldIp + analysis.image.oldIp;
  
  if (totalReplacements === 0) {
    log('✅ 没有需要替换的URL，操作完成。');
    return false;
  }
  
  log('\n⚠️ 即将执行以下替换操作:');
  log('=====================================');
  log(`将要替换 ${totalReplacements} 个URL中的IP地址`);
  log(`从: ${OLD_IP}`);
  log(`到: ${NEW_IP}`);
  log('=====================================');
  
  // 在生产环境中，这里应该有用户确认步骤
  // 为了自动化，我们直接返回true，但建议在实际使用时添加确认机制
  log('⚠️ 注意: 请确保已经完成数据备份！');
  log('继续执行替换操作...');
  
  return true;
}

async function replaceProductUrls(analysis) {
  try {
    log('\n🔄 开始替换Product集合中的URL...');
    
    const db = mongoose.connection.db;
    const productCollection = db.collection('products');
    
    let updatedCount = 0;
    const updateResults = [];
    
    // 按产品分组处理
    const productUpdates = {};
    for (const detail of analysis.product.details) {
      if (!productUpdates[detail.productId]) {
        productUpdates[detail.productId] = {};
      }
      productUpdates[detail.productId][`images.${detail.imageType}`] = detail.newUrl;
    }
    
    for (const [productId, updates] of Object.entries(productUpdates)) {
      try {
        const result = await productCollection.updateOne(
          { productId: productId },
          { $set: updates }
        );
        
        if (result.modifiedCount > 0) {
          updatedCount++;
          updateResults.push({
            productId: productId,
            updates: updates,
            success: true
          });
          log(`✅ 更新产品 ${productId}: ${Object.keys(updates).join(', ')}`);
        }
      } catch (error) {
        log(`❌ 更新产品 ${productId} 失败: ${error.message}`);
        updateResults.push({
          productId: productId,
          updates: updates,
          success: false,
          error: error.message
        });
      }
    }
    
    log(`\n📊 Product集合更新完成: ${updatedCount}/${Object.keys(productUpdates).length} 个产品`);
    return { updatedCount, updateResults };
    
  } catch (error) {
    log(`❌ 替换Product集合URL失败: ${error.message}`);
    throw error;
  }
}

async function replaceImageUrls(analysis) {
  try {
    log('\n🔄 开始替换Image集合中的URL...');

    const db = mongoose.connection.db;
    const imageCollection = db.collection('images');

    let updatedCount = 0;
    const updateResults = [];

    // 按图片ID分组，分别处理每个图片
    const imageGroups = {};
    for (const detail of analysis.image.details) {
      if (!imageGroups[detail.imageId]) {
        imageGroups[detail.imageId] = [];
      }
      imageGroups[detail.imageId].push(detail);
    }

    for (const [imageId, details] of Object.entries(imageGroups)) {
      try {
        // 获取当前图片文档
        const currentImage = await imageCollection.findOne({ imageId: imageId });
        if (!currentImage) {
          log(`⚠️ 图片不存在: ${imageId}`);
          continue;
        }

        // 准备更新对象
        const updates = {};
        let hasUpdates = false;

        for (const detail of details) {
          if (detail.field === 'publicUrl') {
            if (currentImage.publicUrl && currentImage.publicUrl.includes(OLD_IP)) {
              updates.publicUrl = currentImage.publicUrl.replace(new RegExp(OLD_IP, 'g'), NEW_IP);
              hasUpdates = true;
            }
          } else if (detail.field === 'cdnUrl') {
            if (currentImage.cdnUrl && currentImage.cdnUrl.includes(OLD_IP)) {
              updates.cdnUrl = currentImage.cdnUrl.replace(new RegExp(OLD_IP, 'g'), NEW_IP);
              hasUpdates = true;
            }
          } else if (detail.field.startsWith('thumbnails[')) {
            // 处理thumbnails数组
            if (currentImage.thumbnails && Array.isArray(currentImage.thumbnails)) {
              const updatedThumbnails = currentImage.thumbnails.map(thumbnail => {
                if (thumbnail.url && thumbnail.url.includes(OLD_IP)) {
                  return {
                    ...thumbnail,
                    url: thumbnail.url.replace(new RegExp(OLD_IP, 'g'), NEW_IP)
                  };
                }
                return thumbnail;
              });
              updates.thumbnails = updatedThumbnails;
              hasUpdates = true;
            }
          }
        }

        if (hasUpdates) {
          const result = await imageCollection.updateOne(
            { imageId: imageId },
            { $set: updates }
          );

          if (result.modifiedCount > 0) {
            updatedCount++;
            updateResults.push({
              imageId: imageId,
              updates: Object.keys(updates),
              success: true
            });
            log(`✅ 更新图片 ${imageId}: ${Object.keys(updates).join(', ')}`);
          }
        }
      } catch (error) {
        log(`❌ 更新图片 ${imageId} 失败: ${error.message}`);
        updateResults.push({
          imageId: imageId,
          updates: [],
          success: false,
          error: error.message
        });
      }
    }

    log(`\n📊 Image集合更新完成: ${updatedCount}/${Object.keys(imageGroups).length} 个图片`);
    return { updatedCount, updateResults };

  } catch (error) {
    log(`❌ 替换Image集合URL失败: ${error.message}`);
    throw error;
  }
}

async function verifyReplacement() {
  try {
    log('\n🔍 验证替换结果...');
    
    const verificationAnalysis = await analyzeCurrentState();
    
    log('\n📊 验证结果:');
    log('=====================================');
    log(`Product集合剩余旧IP URL: ${verificationAnalysis.product.oldIp}`);
    log(`Image集合剩余旧IP URL: ${verificationAnalysis.image.oldIp}`);
    log(`总计剩余旧IP URL: ${verificationAnalysis.product.oldIp + verificationAnalysis.image.oldIp}`);
    
    const success = (verificationAnalysis.product.oldIp + verificationAnalysis.image.oldIp) === 0;
    
    if (success) {
      log('✅ 验证成功！所有URL都已成功替换。');
    } else {
      log('⚠️ 验证发现仍有URL未替换，请检查日志。');
    }
    
    return { success, analysis: verificationAnalysis };
    
  } catch (error) {
    log(`❌ 验证替换结果失败: ${error.message}`);
    throw error;
  }
}

async function performBatchReplacement() {
  const startTime = Date.now();

  try {
    log('🚀 开始批量IP地址替换操作...');

    // 连接数据库
    await connectToDatabase();

    // 分析当前状态
    const analysis = await analyzeCurrentState();

    // 确认替换操作
    const shouldProceed = await confirmReplacement(analysis);
    if (!shouldProceed) {
      log('✅ 操作完成，无需替换。');
      return { success: true, message: '无需替换' };
    }

    // 执行替换
    log('\n🔄 开始执行替换操作...');

    const productResult = await replaceProductUrls(analysis);
    const imageResult = await replaceImageUrls(analysis);

    // 验证结果
    const verification = await verifyReplacement();

    // 生成最终报告
    const totalTime = Date.now() - startTime;
    const finalReport = {
      timestamp: new Date().toISOString(),
      duration: `${(totalTime / 1000).toFixed(2)} 秒`,
      originalAnalysis: analysis,
      productUpdates: productResult,
      imageUpdates: imageResult,
      verification: verification,
      summary: {
        totalUrlsReplaced: productResult.updatedCount + imageResult.updatedCount,
        productUrlsReplaced: productResult.updatedCount,
        imageUrlsReplaced: imageResult.updatedCount,
        verificationPassed: verification.success
      }
    };

    // 保存报告
    const reportPath = path.join(LOG_DIR, `ip-replacement-report-${DATE_STAMP}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(finalReport, null, 2));

    log('\n✅ 批量替换操作完成！');
    log('=====================================');
    log(`总耗时: ${finalReport.duration}`);
    log(`Product集合更新: ${productResult.updatedCount} 个产品`);
    log(`Image集合更新: ${imageResult.updatedCount} 个图片`);
    log(`验证结果: ${verification.success ? '通过' : '失败'}`);
    log(`详细报告: ${reportPath}`);
    log(`操作日志: ${logFile}`);

    return {
      success: verification.success,
      report: finalReport,
      reportPath: reportPath,
      logPath: logFile
    };

  } catch (error) {
    log(`💥 批量替换操作失败: ${error.message}`);
    log(`错误堆栈: ${error.stack}`);

    return {
      success: false,
      error: error.message,
      logPath: logFile
    };
  } finally {
    // 关闭数据库连接
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
      log('🔌 数据库连接已关闭');
    }
  }
}

// 运行脚本
if (require.main === module) {
  performBatchReplacement()
    .then(result => {
      if (result.success) {
        console.log('\n🎉 IP地址批量替换成功完成！');
        process.exit(0);
      } else {
        console.error('\n❌ IP地址批量替换失败！');
        console.error(`错误: ${result.error}`);
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = {
  connectToDatabase,
  analyzeCurrentState,
  confirmReplacement,
  replaceProductUrls,
  replaceImageUrls,
  verifyReplacement,
  performBatchReplacement
};
