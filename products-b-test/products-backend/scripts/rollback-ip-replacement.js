#!/usr/bin/env node

const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// 配置
const MONGODB_URI = process.env.MONGODB_URI;
const BACKUP_DIR = path.join(__dirname, '../backups');
const LOG_DIR = path.join(__dirname, '../logs');
const DATE_STAMP = new Date().toISOString().slice(0, 19).replace(/:/g, '-');

console.log('🔄 数据库IP替换回滚脚本');
console.log('=====================================');

// 确保日志目录存在
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

const logFile = path.join(LOG_DIR, `ip-rollback-${DATE_STAMP}.log`);

function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}`;
  console.log(message);
  fs.appendFileSync(logFile, logMessage + '\n');
}

async function connectToDatabase() {
  try {
    log('🔌 连接到数据库...');
    await mongoose.connect(MONGODB_URI);
    log('✅ 数据库连接成功');
  } catch (error) {
    log(`❌ 数据库连接失败: ${error.message}`);
    throw error;
  }
}

function findLatestBackup() {
  try {
    log('🔍 查找最新的IP替换备份...');
    
    if (!fs.existsSync(BACKUP_DIR)) {
      throw new Error(`备份目录不存在: ${BACKUP_DIR}`);
    }
    
    const backupDirs = fs.readdirSync(BACKUP_DIR)
      .filter(dir => dir.startsWith('ip-replacement-backup-'))
      .sort()
      .reverse();
    
    if (backupDirs.length === 0) {
      throw new Error('未找到IP替换备份目录');
    }
    
    const latestBackupDir = backupDirs[0];
    const backupPath = path.join(BACKUP_DIR, latestBackupDir);
    const metadataPath = path.join(backupPath, 'backup-metadata.json');
    
    if (!fs.existsSync(metadataPath)) {
      throw new Error(`备份元数据文件不存在: ${metadataPath}`);
    }
    
    const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
    
    log(`📁 找到备份: ${latestBackupDir}`);
    log(`📅 备份时间: ${metadata.timestamp}`);
    log(`📊 备份类型: ${metadata.type}`);
    
    return {
      backupPath: backupPath,
      metadata: metadata
    };
    
  } catch (error) {
    log(`❌ 查找备份失败: ${error.message}`);
    throw error;
  }
}

function selectBackup() {
  try {
    log('📋 列出所有可用的IP替换备份...');
    
    if (!fs.existsSync(BACKUP_DIR)) {
      throw new Error(`备份目录不存在: ${BACKUP_DIR}`);
    }
    
    const backupDirs = fs.readdirSync(BACKUP_DIR)
      .filter(dir => dir.startsWith('ip-replacement-backup-'))
      .sort()
      .reverse();
    
    if (backupDirs.length === 0) {
      throw new Error('未找到IP替换备份目录');
    }
    
    log('\n可用的备份:');
    log('=====================================');
    
    const backupOptions = [];
    for (let i = 0; i < backupDirs.length; i++) {
      const backupDir = backupDirs[i];
      const backupPath = path.join(BACKUP_DIR, backupDir);
      const metadataPath = path.join(backupPath, 'backup-metadata.json');
      
      if (fs.existsSync(metadataPath)) {
        try {
          const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
          log(`${i + 1}. ${backupDir}`);
          log(`   时间: ${metadata.timestamp}`);
          log(`   预计影响: ${metadata.replacementPlan?.estimatedChanges || 'N/A'} 个URL`);
          
          backupOptions.push({
            index: i + 1,
            backupPath: backupPath,
            metadata: metadata
          });
        } catch (error) {
          log(`   ⚠️ 无法读取元数据: ${error.message}`);
        }
      }
    }
    
    // 默认选择最新的备份
    if (backupOptions.length > 0) {
      log(`\n🎯 自动选择最新备份: ${backupOptions[0].index}`);
      return backupOptions[0];
    } else {
      throw new Error('没有可用的有效备份');
    }
    
  } catch (error) {
    log(`❌ 选择备份失败: ${error.message}`);
    throw error;
  }
}

async function restoreCollection(collectionName, backupPath) {
  try {
    log(`🔄 恢复集合: ${collectionName}`);
    
    const collectionFile = path.join(backupPath, `${collectionName}.json`);
    
    if (!fs.existsSync(collectionFile)) {
      log(`   ⚠️ 备份文件不存在，跳过: ${collectionFile}`);
      return { collection: collectionName, status: 'skipped', reason: 'file_not_found' };
    }
    
    const backupData = JSON.parse(fs.readFileSync(collectionFile, 'utf8'));
    
    if (!Array.isArray(backupData) || backupData.length === 0) {
      log(`   ⚠️ 备份数据为空，跳过: ${collectionName}`);
      return { collection: collectionName, status: 'skipped', reason: 'empty_data' };
    }
    
    const db = mongoose.connection.db;
    const collection = db.collection(collectionName);
    
    // 清空现有数据
    log(`   🗑️ 清空现有数据...`);
    await collection.deleteMany({});
    
    // 恢复备份数据
    log(`   📥 恢复 ${backupData.length} 条记录...`);
    await collection.insertMany(backupData);
    
    log(`   ✅ 恢复完成: ${collectionName}`);
    
    return {
      collection: collectionName,
      status: 'success',
      recordsRestored: backupData.length
    };
    
  } catch (error) {
    log(`   ❌ 恢复集合 ${collectionName} 失败: ${error.message}`);
    return {
      collection: collectionName,
      status: 'failed',
      error: error.message
    };
  }
}

async function performRollback(backupInfo) {
  const startTime = Date.now();
  
  try {
    log('\n🚀 开始执行回滚操作...');
    log(`📁 使用备份: ${path.basename(backupInfo.backupPath)}`);
    
    // 连接数据库
    await connectToDatabase();
    
    // 获取需要恢复的集合
    const collectionsToRestore = ['products', 'images'];
    const restoreResults = [];
    
    for (const collectionName of collectionsToRestore) {
      const result = await restoreCollection(collectionName, backupInfo.backupPath);
      restoreResults.push(result);
    }
    
    // 统计结果
    const successfulRestores = restoreResults.filter(r => r.status === 'success');
    const failedRestores = restoreResults.filter(r => r.status === 'failed');
    const skippedRestores = restoreResults.filter(r => r.status === 'skipped');
    
    const totalTime = Date.now() - startTime;
    
    // 生成回滚报告
    const rollbackReport = {
      timestamp: new Date().toISOString(),
      duration: `${(totalTime / 1000).toFixed(2)} 秒`,
      backupUsed: {
        path: backupInfo.backupPath,
        metadata: backupInfo.metadata
      },
      restoreResults: restoreResults,
      summary: {
        totalCollections: collectionsToRestore.length,
        successfulRestores: successfulRestores.length,
        failedRestores: failedRestores.length,
        skippedRestores: skippedRestores.length,
        totalRecordsRestored: successfulRestores.reduce((sum, r) => sum + (r.recordsRestored || 0), 0)
      }
    };
    
    // 保存回滚报告
    const reportPath = path.join(LOG_DIR, `ip-rollback-report-${DATE_STAMP}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(rollbackReport, null, 2));
    
    log('\n✅ 回滚操作完成！');
    log('=====================================');
    log(`总耗时: ${rollbackReport.duration}`);
    log(`成功恢复: ${successfulRestores.length} 个集合`);
    log(`失败: ${failedRestores.length} 个集合`);
    log(`跳过: ${skippedRestores.length} 个集合`);
    log(`总记录数: ${rollbackReport.summary.totalRecordsRestored}`);
    log(`详细报告: ${reportPath}`);
    log(`操作日志: ${logFile}`);
    
    if (failedRestores.length > 0) {
      log('\n⚠️ 失败的集合:');
      failedRestores.forEach(f => {
        log(`  - ${f.collection}: ${f.error}`);
      });
    }
    
    return {
      success: failedRestores.length === 0,
      report: rollbackReport,
      reportPath: reportPath,
      logPath: logFile
    };
    
  } catch (error) {
    log(`💥 回滚操作失败: ${error.message}`);
    log(`错误堆栈: ${error.stack}`);
    
    return {
      success: false,
      error: error.message,
      logPath: logFile
    };
  } finally {
    // 关闭数据库连接
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
      log('🔌 数据库连接已关闭');
    }
  }
}

// 运行回滚脚本
if (require.main === module) {
  (async () => {
    try {
      // 选择备份
      const backupInfo = selectBackup();
      
      // 执行回滚
      const result = await performRollback(backupInfo);
      
      if (result.success) {
        console.log('\n🎉 数据库回滚成功完成！');
        process.exit(0);
      } else {
        console.error('\n❌ 数据库回滚失败！');
        console.error(`错误: ${result.error}`);
        process.exit(1);
      }
    } catch (error) {
      console.error('💥 回滚脚本执行失败:', error);
      process.exit(1);
    }
  })();
}

module.exports = {
  findLatestBackup,
  selectBackup,
  restoreCollection,
  performRollback
};
