/**
 * 检查前后端配置一致性
 * 确保图片服务配置在前后端保持一致
 */

const fs = require('fs');
const path = require('path');

/**
 * 解析.env文件
 */
function parseEnvFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const env = {};
    
    content.split('\n').forEach(line => {
      line = line.trim();
      if (line && !line.startsWith('#')) {
        const [key, ...valueParts] = line.split('=');
        if (key && valueParts.length > 0) {
          env[key.trim()] = valueParts.join('=').trim();
        }
      }
    });
    
    return env;
  } catch (error) {
    console.error(`❌ 无法读取文件 ${filePath}:`, error.message);
    return null;
  }
}

/**
 * 检查配置一致性
 */
function checkConfigConsistency() {
  console.log('🔍 检查前后端配置一致性...\n');
  
  // 读取后端配置
  const backendEnvPath = path.join(__dirname, '.env');
  const backendEnv = parseEnvFile(backendEnvPath);
  
  if (!backendEnv) {
    console.error('❌ 无法读取后端配置文件');
    return false;
  }
  
  // 读取前端配置
  const frontendEnvPath = path.join(__dirname, '../product-showcase/.env');
  const frontendEnv = parseEnvFile(frontendEnvPath);
  
  if (!frontendEnv) {
    console.error('❌ 无法读取前端配置文件');
    return false;
  }
  
  console.log('📋 配置对比:');
  
  // 检查MinIO端点配置
  const backendMinioEndpoint = backendEnv.MINIO_ENDPOINT;
  const backendMinioPort = backendEnv.MINIO_PORT || '9000';
  const backendImageUrl = `http://${backendMinioEndpoint}:${backendMinioPort}`;
  
  const frontendImageUrl = frontendEnv.VITE_IMAGE_BASE_URL;
  
  console.log(`   后端图片服务地址: ${backendImageUrl}`);
  console.log(`   前端图片服务地址: ${frontendImageUrl}`);
  
  const isConsistent = backendImageUrl === frontendImageUrl;
  
  if (isConsistent) {
    console.log('✅ 前后端图片服务配置一致');
  } else {
    console.log('❌ 前后端图片服务配置不一致');
    console.log('   建议修复:');
    console.log(`     - 后端 MINIO_ENDPOINT 应设置为与前端一致的IP地址`);
    console.log(`     - 或者前端 VITE_IMAGE_BASE_URL 应设置为与后端一致`);
  }
  
  // 检查API地址配置
  console.log('\n📋 API配置对比:');
  const frontendApiUrl = frontendEnv.VITE_API_BASE_URL;
  console.log(`   前端API地址: ${frontendApiUrl}`);
  
  // 其他重要配置检查
  console.log('\n📋 其他配置检查:');
  console.log(`   后端MinIO桶名: ${backendEnv.MINIO_BUCKET || 'product-images'}`);
  console.log(`   前端图片优化: ${frontendEnv.VITE_ENABLE_IMAGE_OPTIMIZATION || 'false'}`);
  console.log(`   前端图片质量: ${frontendEnv.VITE_DEFAULT_IMAGE_QUALITY || '85'}`);
  
  return isConsistent;
}

/**
 * 生成配置建议
 */
function generateConfigRecommendations() {
  console.log('\n💡 配置建议:');
  console.log('1. 确保前后端使用相同的图片服务地址');
  console.log('2. 在生产环境中，使用实际的服务器IP地址而不是localhost');
  console.log('3. 定期检查配置一致性，特别是在部署后');
  console.log('4. 考虑使用环境变量管理工具统一管理配置');
  console.log('5. 在CI/CD流程中加入配置一致性检查');
}

/**
 * 主执行函数
 */
function main() {
  console.log('🚀 开始配置一致性检查...\n');
  
  const isConsistent = checkConfigConsistency();
  
  generateConfigRecommendations();
  
  console.log('\n📊 检查结果:');
  if (isConsistent) {
    console.log('✅ 配置检查通过，前后端配置一致');
    console.log('🎉 localhost图片URL问题已完全解决！');
  } else {
    console.log('⚠️  配置存在不一致，建议修复后重新检查');
  }
  
  return isConsistent;
}

// 执行检查
if (require.main === module) {
  const result = main();
  process.exit(result ? 0 : 1);
}

module.exports = { checkConfigConsistency, parseEnvFile };
