{"permissions": {"allow": ["Bash(npm run dev:*)", "Bash(lsof:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(curl:*)", "Bash(kill:*)", "Bash(pm2 list:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm run build:*)", "Bash(npm install:*)", "Bash(pm2 start:*)", "Bash(pm2 delete:*)", "Bash(pm2 logs:*)", "Bash(pm2 status:*)", "Bash(pm2 restart:*)", "Bash(pm2 save:*)", "<PERSON><PERSON>(chmod:*)", "Bash(/root/products-b-test/products-b-test/pm2-manager.sh status:*)", "Bash(nginx:*)", "Bash(systemctl status:*)", "Bash(ln:*)", "Bash(systemctl reload:*)", "Bash(ss:*)", "Bash(find:*)"], "deny": []}}